#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الكاميرات - إدارة الاتصال والبث
Camera Manager - Handle connections and streaming
"""

import cv2
import threading
import time
import base64
import logging
from typing import Dict, Optional, Callable
import numpy as np
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class CameraStream:
    """فئة إدارة بث كاميرا واحدة"""
    
    def __init__(self, camera_id: int, url: str, camera_type: str = 'rtsp', 
                 username: str = None, password: str = None):
        self.camera_id = camera_id
        self.url = url
        self.camera_type = camera_type.lower()
        self.username = username
        self.password = password
        
        self.cap = None
        self.is_streaming = False
        self.is_recording = False
        self.thread = None
        self.frame_callback = None
        self.status_callback = None
        
        self.last_frame = None
        self.fps = 25
        self.frame_count = 0
        self.start_time = time.time()
        
        # إعدادات كشف الحركة
        self.motion_detection = False
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2()
        self.motion_threshold = 500
        
        # إعدادات التسجيل
        self.video_writer = None
        self.recording_path = None
        
    def _build_url(self) -> str:
        """بناء رابط الكاميرا مع بيانات الاعتماد"""
        if self.camera_type == 'rtsp' and self.username and self.password:
            parsed = urlparse(self.url)
            return f"{parsed.scheme}://{self.username}:{self.password}@{parsed.netloc}{parsed.path}"
        return self.url
    
    def connect(self) -> bool:
        """الاتصال بالكاميرا"""
        try:
            if self.camera_type == 'usb':
                # كاميرا USB
                camera_index = int(self.url) if self.url.isdigit() else 0
                self.cap = cv2.VideoCapture(camera_index)
            else:
                # كاميرا IP/RTSP
                full_url = self._build_url()
                self.cap = cv2.VideoCapture(full_url)
                
            if not self.cap or not self.cap.isOpened():
                logger.error(f"فشل في الاتصال بالكاميرا {self.camera_id}")
                return False
                
            # إعداد خصائص الكاميرا
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            
            # اختبار قراءة إطار
            ret, frame = self.cap.read()
            if not ret:
                logger.error(f"فشل في قراءة إطار من الكاميرا {self.camera_id}")
                return False
                
            logger.info(f"تم الاتصال بنجاح بالكاميرا {self.camera_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في الاتصال بالكاميرا {self.camera_id}: {str(e)}")
            return False
    
    def start_streaming(self, frame_callback: Callable = None, status_callback: Callable = None):
        """بدء البث المباشر"""
        if self.is_streaming:
            return
            
        self.frame_callback = frame_callback
        self.status_callback = status_callback
        
        if not self.connect():
            if self.status_callback:
                self.status_callback(self.camera_id, 'offline')
            return
            
        self.is_streaming = True
        self.thread = threading.Thread(target=self._stream_loop, daemon=True)
        self.thread.start()
        
        if self.status_callback:
            self.status_callback(self.camera_id, 'online')
            
        logger.info(f"بدء البث للكاميرا {self.camera_id}")
    
    def stop_streaming(self):
        """إيقاف البث"""
        self.is_streaming = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2)
            
        if self.cap:
            self.cap.release()
            self.cap = None
            
        if self.status_callback:
            self.status_callback(self.camera_id, 'offline')
            
        logger.info(f"تم إيقاف البث للكاميرا {self.camera_id}")
    
    def _stream_loop(self):
        """حلقة البث الرئيسية"""
        consecutive_failures = 0
        max_failures = 5
        
        while self.is_streaming:
            try:
                if not self.cap or not self.cap.isOpened():
                    if not self.connect():
                        consecutive_failures += 1
                        if consecutive_failures >= max_failures:
                            logger.error(f"فشل متكرر في الاتصال بالكاميرا {self.camera_id}")
                            break
                        time.sleep(2)
                        continue
                
                ret, frame = self.cap.read()
                if not ret:
                    consecutive_failures += 1
                    if consecutive_failures >= max_failures:
                        logger.error(f"فشل في قراءة الإطارات من الكاميرا {self.camera_id}")
                        break
                    time.sleep(0.1)
                    continue
                
                consecutive_failures = 0
                self.frame_count += 1
                self.last_frame = frame.copy()
                
                # معالجة الإطار
                processed_frame = self._process_frame(frame)
                
                # إرسال الإطار عبر callback
                if self.frame_callback:
                    encoded_frame = self._encode_frame(processed_frame)
                    self.frame_callback(self.camera_id, encoded_frame)
                
                # التسجيل إذا كان مفعلاً
                if self.is_recording and self.video_writer:
                    self.video_writer.write(processed_frame)
                
                # تنظيم معدل الإطارات
                time.sleep(1.0 / self.fps)
                
            except Exception as e:
                logger.error(f"خطأ في حلقة البث للكاميرا {self.camera_id}: {str(e)}")
                consecutive_failures += 1
                if consecutive_failures >= max_failures:
                    break
                time.sleep(1)
        
        # تنظيف الموارد
        self.is_streaming = False
        if self.status_callback:
            self.status_callback(self.camera_id, 'offline')
    
    def _process_frame(self, frame):
        """معالجة الإطار (كشف الحركة، تحسينات...)"""
        processed = frame.copy()
        
        # إضافة الطابع الزمني
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        cv2.putText(processed, timestamp, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # إضافة معلومات الكاميرا
        camera_info = f"Camera {self.camera_id} | FPS: {self._calculate_fps():.1f}"
        cv2.putText(processed, camera_info, (10, processed.shape[0] - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # كشف الحركة إذا كان مفعلاً
        if self.motion_detection:
            motion_detected = self._detect_motion(frame)
            if motion_detected:
                cv2.putText(processed, "MOTION DETECTED", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        return processed
    
    def _detect_motion(self, frame) -> bool:
        """كشف الحركة في الإطار"""
        try:
            # تطبيق Background Subtraction
            fg_mask = self.background_subtractor.apply(frame)
            
            # تنظيف القناع
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
            
            # حساب مساحة الحركة
            motion_area = cv2.countNonZero(fg_mask)
            
            return motion_area > self.motion_threshold
            
        except Exception as e:
            logger.error(f"خطأ في كشف الحركة للكاميرا {self.camera_id}: {str(e)}")
            return False
    
    def _calculate_fps(self) -> float:
        """حساب معدل الإطارات الفعلي"""
        if self.frame_count == 0:
            return 0.0
        elapsed_time = time.time() - self.start_time
        return self.frame_count / elapsed_time if elapsed_time > 0 else 0.0
    
    def _encode_frame(self, frame) -> str:
        """تشفير الإطار إلى base64"""
        try:
            # ضغط الإطار
            _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
            # تحويل إلى base64
            encoded = base64.b64encode(buffer).decode('utf-8')
            return encoded
        except Exception as e:
            logger.error(f"خطأ في تشفير الإطار للكاميرا {self.camera_id}: {str(e)}")
            return ""
    
    def start_recording(self, output_path: str) -> bool:
        """بدء التسجيل"""
        try:
            if not self.last_frame is None:
                height, width = self.last_frame.shape[:2]
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                self.video_writer = cv2.VideoWriter(output_path, fourcc, self.fps, (width, height))
                self.recording_path = output_path
                self.is_recording = True
                logger.info(f"بدء التسجيل للكاميرا {self.camera_id}: {output_path}")
                return True
        except Exception as e:
            logger.error(f"خطأ في بدء التسجيل للكاميرا {self.camera_id}: {str(e)}")
        return False
    
    def stop_recording(self):
        """إيقاف التسجيل"""
        if self.is_recording:
            self.is_recording = False
            if self.video_writer:
                self.video_writer.release()
                self.video_writer = None
            logger.info(f"تم إيقاف التسجيل للكاميرا {self.camera_id}")
    
    def take_snapshot(self, output_path: str) -> bool:
        """التقاط لقطة شاشة"""
        try:
            if self.last_frame is not None:
                cv2.imwrite(output_path, self.last_frame)
                logger.info(f"تم التقاط لقطة للكاميرا {self.camera_id}: {output_path}")
                return True
        except Exception as e:
            logger.error(f"خطأ في التقاط لقطة للكاميرا {self.camera_id}: {str(e)}")
        return False
    
    def set_motion_detection(self, enabled: bool):
        """تفعيل/إلغاء كشف الحركة"""
        self.motion_detection = enabled
        logger.info(f"كشف الحركة للكاميرا {self.camera_id}: {'مفعل' if enabled else 'معطل'}")


class CameraManager:
    """مدير جميع الكاميرات"""
    
    def __init__(self):
        self.cameras: Dict[int, CameraStream] = {}
        self.frame_callbacks = []
        self.status_callbacks = []
    
    def add_camera(self, camera_id: int, url: str, camera_type: str = 'rtsp',
                   username: str = None, password: str = None) -> bool:
        """إضافة كاميرا جديدة"""
        try:
            camera = CameraStream(camera_id, url, camera_type, username, password)
            self.cameras[camera_id] = camera
            logger.info(f"تم إضافة الكاميرا {camera_id}")
            return True
        except Exception as e:
            logger.error(f"خطأ في إضافة الكاميرا {camera_id}: {str(e)}")
            return False
    
    def remove_camera(self, camera_id: int):
        """إزالة كاميرا"""
        if camera_id in self.cameras:
            self.cameras[camera_id].stop_streaming()
            del self.cameras[camera_id]
            logger.info(f"تم إزالة الكاميرا {camera_id}")
    
    def start_all_streaming(self):
        """بدء البث لجميع الكاميرات"""
        for camera in self.cameras.values():
            camera.start_streaming(self._frame_callback, self._status_callback)
    
    def stop_all_streaming(self):
        """إيقاف البث لجميع الكاميرات"""
        for camera in self.cameras.values():
            camera.stop_streaming()
    
    def start_streaming(self, camera_id: int):
        """بدء البث لكاميرا محددة"""
        if camera_id in self.cameras:
            self.cameras[camera_id].start_streaming(self._frame_callback, self._status_callback)
    
    def stop_streaming(self, camera_id: int):
        """إيقاف البث لكاميرا محددة"""
        if camera_id in self.cameras:
            self.cameras[camera_id].stop_streaming()
    
    def _frame_callback(self, camera_id: int, encoded_frame: str):
        """استدعاء عند وصول إطار جديد"""
        for callback in self.frame_callbacks:
            try:
                callback(camera_id, encoded_frame)
            except Exception as e:
                logger.error(f"خطأ في frame callback: {str(e)}")
    
    def _status_callback(self, camera_id: int, status: str):
        """استدعاء عند تغيير حالة الكاميرا"""
        for callback in self.status_callbacks:
            try:
                callback(camera_id, status)
            except Exception as e:
                logger.error(f"خطأ في status callback: {str(e)}")
    
    def add_frame_callback(self, callback: Callable):
        """إضافة callback للإطارات"""
        self.frame_callbacks.append(callback)
    
    def add_status_callback(self, callback: Callable):
        """إضافة callback لحالة الكاميرات"""
        self.status_callbacks.append(callback)
    
    def get_camera_status(self, camera_id: int) -> str:
        """الحصول على حالة كاميرا"""
        if camera_id in self.cameras:
            return 'online' if self.cameras[camera_id].is_streaming else 'offline'
        return 'not_found'
    
    def get_all_status(self) -> Dict[int, str]:
        """الحصول على حالة جميع الكاميرات"""
        return {cid: self.get_camera_status(cid) for cid in self.cameras.keys()}
