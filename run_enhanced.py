#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام المراقبة الذكي المحسن
Enhanced Smart Surveillance System Runner
"""

import os
import sys
import time
import logging
from app import app, socketio, enhanced_camera_manager, db

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/enhanced_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'logs',
        'recordings', 
        'static/uploads',
        'static/events',
        'static/snapshots',
        'known_faces'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"تم إنشاء المجلد: {directory}")

def initialize_database():
    """إنشاء قاعدة البيانات والبيانات الافتراضية"""
    try:
        with app.app_context():
            db.create_all()
            logger.info("تم إنشاء قاعدة البيانات بنجاح")
            
            # إنشاء مستخدم افتراضي
            from models import User
            if not User.query.filter_by(username='admin').first():
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    role='admin'
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                logger.info("تم إنشاء المستخدم الافتراضي: admin/admin123")
                
    except Exception as e:
        logger.error(f"خطأ في إنشاء قاعدة البيانات: {e}")
        return False
    
    return True

def setup_test_cameras():
    """إضافة كاميرات تجريبية للاختبار"""
    try:
        # كاميرات تجريبية مع عناوين مختلفة
        test_cameras = [
            {
                'id': 1,
                'url': 'rtsp://admin:admin@192.168.1.100:554/stream1',
                'username': 'admin',
                'password': 'admin'
            },
            {
                'id': 2, 
                'url': 'rtsp://admin:admin@192.168.1.101:554/stream1',
                'username': 'admin',
                'password': 'admin'
            },
            {
                'id': 3,
                'url': 'rtsp://admin:admin@192.168.1.102:554/stream1', 
                'username': 'admin',
                'password': 'admin'
            },
            {
                'id': 4,
                'url': 'rtsp://admin:admin@192.168.1.103:554/stream1',
                'username': 'admin', 
                'password': 'admin'
            }
        ]
        
        for camera in test_cameras:
            success = enhanced_camera_manager.add_camera(
                camera['id'],
                camera['url'],
                camera['username'],
                camera['password']
            )
            if success:
                logger.info(f"تم إضافة الكاميرا التجريبية {camera['id']}")
            else:
                logger.warning(f"فشل في إضافة الكاميرا التجريبية {camera['id']}")
                
        logger.info("تم إعداد الكاميرات التجريبية")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في إعداد الكاميرات التجريبية: {e}")
        return False

def start_camera_streams():
    """بدء بث جميع الكاميرات"""
    try:
        logger.info("بدء تشغيل بث الكاميرات...")
        started_count = enhanced_camera_manager.start_all_cameras()
        logger.info(f"تم بدء {started_count} كاميرا بنجاح")
        return True
    except Exception as e:
        logger.error(f"خطأ في بدء بث الكاميرات: {e}")
        return False

def print_startup_info():
    """طباعة معلومات بدء التشغيل"""
    print("\n" + "="*60)
    print("🎥 نظام المراقبة الذكي المحسن")
    print("Enhanced Smart Surveillance System")
    print("="*60)
    print(f"📅 التاريخ: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 العنوان: http://localhost:5000")
    print(f"👤 المستخدم الافتراضي: admin")
    print(f"🔑 كلمة المرور: admin123")
    print("="*60)
    print("✅ الميزات المتاحة:")
    print("   • البث المباشر المحسن")
    print("   • الاكتشاف التلقائي للكاميرات")
    print("   • كشف الحركة والوجوه")
    print("   • التسجيل التلقائي")
    print("   • واجهة عربية متكاملة")
    print("="*60)
    print("🚀 النظام جاهز للاستخدام!")
    print("="*60 + "\n")

def main():
    """الدالة الرئيسية"""
    try:
        print("🔄 بدء تشغيل نظام المراقبة الذكي المحسن...")
        
        # إنشاء المجلدات
        create_directories()
        
        # إنشاء قاعدة البيانات
        if not initialize_database():
            logger.error("فشل في إنشاء قاعدة البيانات")
            sys.exit(1)
        
        # إعداد الكاميرات التجريبية
        setup_test_cameras()
        
        # بدء بث الكاميرات
        start_camera_streams()
        
        # طباعة معلومات بدء التشغيل
        print_startup_info()
        
        # تشغيل الخادم
        logger.info("🚀 بدء تشغيل خادم الويب...")
        socketio.run(
            app,
            host='0.0.0.0',
            port=5000,
            debug=False,  # تعطيل debug في الإنتاج
            allow_unsafe_werkzeug=True
        )
        
    except KeyboardInterrupt:
        logger.info("تم إيقاف النظام بواسطة المستخدم")
        cleanup()
    except Exception as e:
        logger.error(f"خطأ في تشغيل النظام: {e}")
        cleanup()
        sys.exit(1)

def cleanup():
    """تنظيف الموارد عند الإغلاق"""
    try:
        logger.info("جاري تنظيف الموارد...")
        enhanced_camera_manager.stop_all_cameras()
        enhanced_camera_manager.cleanup()
        logger.info("تم تنظيف الموارد بنجاح")
    except Exception as e:
        logger.error(f"خطأ في تنظيف الموارد: {e}")

if __name__ == '__main__':
    main()
