# نظام المراقبة الذكي - المكتبات المطلوبة
# Smart Surveillance System - Required Libraries

# Flask Framework
Flask==2.3.3
Flask-SocketIO==5.3.6
Flask-Login==0.6.3
Flask-SQLAlchemy==3.0.5
Flask-WTF==1.1.1
WTForms==3.0.1

# Computer Vision & AI
opencv-python==4.8.1.78
opencv-contrib-python==4.8.1.78
face-recognition==1.3.0
dlib==19.24.2
Pillow==10.0.1

# Deep Learning (YOLO)
torch==2.0.1
torchvision==0.15.2
ultralytics==8.0.196

# Video Processing
imageio==2.31.5
imageio-ffmpeg==0.4.9

# ONVIF Support
onvif-zeep==0.2.12
zeep==4.2.1

# Web Server & Async
gunicorn==21.2.0
gevent==23.7.0
gevent-websocket==0.10.1
eventlet==0.33.3

# Database
SQLAlchemy==2.0.21

# Utilities
python-dotenv==1.0.0
requests==2.31.0
schedule==1.2.0
python-telegram-bot==20.5
smtplib-ssl==1.0.0

# Date & Time
pytz==2023.3
python-dateutil==2.8.2

# File Management
watchdog==3.0.0
send2trash==1.8.2

# Configuration
pyyaml==6.0.1
configparser==6.0.0

# Security
bcrypt==4.0.1
cryptography==41.0.4

# Networking
netifaces==0.11.0
psutil==5.9.5

# Image Processing
scikit-image==0.21.0
numpy==1.24.4
matplotlib==3.7.2

# Web Scraping (for camera discovery)
beautifulsoup4==4.12.2
lxml==4.9.3

# Logging
colorlog==6.7.0

# Testing (optional)
pytest==7.4.2
pytest-flask==1.2.0
