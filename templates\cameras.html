{% extends "base.html" %}

{% block title %}إدارة الكاميرات - نظام المراقبة الذكي{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-video me-2"></i>
                    إدارة الكاميرات
                </h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة كاميرا جديدة
                </button>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ cameras|length }}</div>
                        <div class="stats-label">إجمالي الكاميرات</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-video fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="onlineCameras">0</div>
                        <div class="stats-label">متصلة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-wifi fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="recordingCameras">0</div>
                        <div class="stats-label">تسجل الآن</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-record-vinyl fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="offlineCameras">0</div>
                        <div class="stats-label">غير متصلة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أزرار التحكم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-success" onclick="connectAllCameras()">
                            <i class="fas fa-play me-1"></i>
                            تشغيل الكل
                        </button>
                        <button type="button" class="btn btn-danger" onclick="disconnectAllCameras()">
                            <i class="fas fa-stop me-1"></i>
                            إيقاف الكل
                        </button>
                    </div>
                    
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-primary" onclick="refreshCameraList()">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث القائمة
                        </button>
                        <button type="button" class="btn btn-info" onclick="testAllConnections()">
                            <i class="fas fa-network-wired me-1"></i>
                            اختبار الاتصال
                        </button>
                    </div>
                    
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-warning" onclick="exportCameraSettings()">
                            <i class="fas fa-download me-1"></i>
                            تصدير الإعدادات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="importCameraSettings()">
                            <i class="fas fa-upload me-1"></i>
                            استيراد الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- قائمة الكاميرات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الكاميرات
                    </h5>
                </div>
                <div class="card-body">
                    {% if cameras %}
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>العنوان</th>
                                    <th>الحالة</th>
                                    <th>آخر اتصال</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="camerasTableBody">
                                {% for camera in cameras %}
                                <tr data-camera-id="{{ camera.id }}">
                                    <td>{{ camera.id }}</td>
                                    <td>
                                        <strong>{{ camera.name }}</strong>
                                        {% if not camera.is_active %}
                                        <span class="badge bg-secondary ms-1">معطل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ camera.type.upper() }}</span>
                                    </td>
                                    <td>
                                        <code class="text-muted">{{ camera.url[:50] }}{% if camera.url|length > 50 %}...{% endif %}</code>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary status-badge" id="status-{{ camera.id }}">
                                            <i class="fas fa-circle me-1"></i>
                                            غير معروف
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted" id="lastSeen-{{ camera.id }}">-</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-success" onclick="connectCamera({{ camera.id }})" title="تشغيل">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="disconnectCamera({{ camera.id }})" title="إيقاف">
                                                <i class="fas fa-stop"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="testCamera({{ camera.id }})" title="اختبار">
                                                <i class="fas fa-network-wired"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="editCamera({{ camera.id }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteCamera({{ camera.id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-video-slash fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد كاميرات مضافة</h4>
                        <p class="text-muted">ابدأ بإضافة كاميرا جديدة لبدء المراقبة</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة كاميرا الآن
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة كاميرا -->
<div class="modal fade" id="addCameraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة كاميرا جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraName" class="form-label">اسم الكاميرا *</label>
                                <input type="text" class="form-control" id="cameraName" required
                                       placeholder="مثال: كاميرا المدخل الرئيسي">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraType" class="form-label">نوع الكاميرا *</label>
                                <select class="form-select" id="cameraType" required onchange="updateUrlPlaceholder()">
                                    <option value="">اختر نوع الكاميرا</option>
                                    <option value="rtsp">RTSP (IP Camera)</option>
                                    <option value="http">HTTP (MJPEG)</option>
                                    <option value="usb">USB Camera</option>
                                    <option value="onvif">ONVIF</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="cameraUrl" class="form-label">عنوان الكاميرا *</label>
                        <input type="text" class="form-control" id="cameraUrl" required
                               placeholder="سيتم تحديثه حسب نوع الكاميرا">
                        <div class="form-text" id="urlHelp">
                            أمثلة على العناوين ستظهر هنا حسب النوع المختار
                        </div>
                    </div>
                    
                    <div class="row" id="credentialsSection" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraUsername" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="cameraUsername"
                                       placeholder="admin">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraPassword" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="cameraPassword"
                                       placeholder="كلمة المرور">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraResolution" class="form-label">الدقة</label>
                                <select class="form-select" id="cameraResolution">
                                    <option value="1920x1080">1080p (1920×1080)</option>
                                    <option value="1280x720">720p (1280×720)</option>
                                    <option value="640x480">480p (640×480)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraFps" class="form-label">معدل الإطارات</label>
                                <select class="form-select" id="cameraFps">
                                    <option value="30">30 FPS</option>
                                    <option value="25" selected>25 FPS</option>
                                    <option value="15">15 FPS</option>
                                    <option value="10">10 FPS</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cameraActive" checked>
                            <label class="form-check-label" for="cameraActive">
                                تفعيل الكاميرا فور الإضافة
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="testCameraConnection()">
                    <i class="fas fa-network-wired me-1"></i>
                    اختبار الاتصال
                </button>
                <button type="button" class="btn btn-primary" onclick="addCamera()">
                    <i class="fas fa-plus me-1"></i>
                    إضافة الكاميرا
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تعديل الكاميرا -->
<div class="modal fade" id="editCameraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الكاميرا
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- نفس النموذج مع بيانات محملة -->
                <form id="editCameraForm">
                    <input type="hidden" id="editCameraId">
                    <!-- باقي الحقول مثل نموذج الإضافة -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="updateCamera()">
                    <i class="fas fa-save me-1"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.status-badge.bg-success { animation: pulse 2s infinite; }
.status-badge.bg-danger { animation: none; }

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.table td {
    vertical-align: middle;
}

.btn-group-sm .btn {
    margin: 0 1px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let socket = io();

// تحديث placeholder حسب نوع الكاميرا
function updateUrlPlaceholder() {
    const type = document.getElementById('cameraType').value;
    const urlInput = document.getElementById('cameraUrl');
    const helpText = document.getElementById('urlHelp');
    const credentialsSection = document.getElementById('credentialsSection');
    
    const examples = {
        'rtsp': {
            placeholder: 'rtsp://*************:554/stream1',
            help: 'مثال: rtsp://*************:554/stream1 أو rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0',
            showCredentials: true
        },
        'http': {
            placeholder: 'http://*************/mjpeg',
            help: 'مثال: http://*************/mjpeg أو http://*************/video.cgi',
            showCredentials: true
        },
        'usb': {
            placeholder: '0',
            help: 'رقم الكاميرا: 0 للكاميرا الأولى، 1 للثانية، إلخ',
            showCredentials: false
        },
        'onvif': {
            placeholder: 'http://*************:80/onvif/device_service',
            help: 'مثال: http://*************:80/onvif/device_service',
            showCredentials: true
        }
    };
    
    if (examples[type]) {
        urlInput.placeholder = examples[type].placeholder;
        helpText.textContent = examples[type].help;
        credentialsSection.style.display = examples[type].showCredentials ? 'block' : 'none';
    }
}

// إضافة كاميرا جديدة
function addCamera() {
    const form = document.getElementById('addCameraForm');
    const formData = new FormData(form);
    
    const cameraData = {
        name: document.getElementById('cameraName').value,
        type: document.getElementById('cameraType').value,
        url: document.getElementById('cameraUrl').value,
        username: document.getElementById('cameraUsername').value,
        password: document.getElementById('cameraPassword').value,
        resolution: document.getElementById('cameraResolution').value,
        fps: document.getElementById('cameraFps').value,
        is_active: document.getElementById('cameraActive').checked
    };
    
    // التحقق من البيانات
    if (!cameraData.name || !cameraData.type || !cameraData.url) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // إرسال البيانات
    fetch('/api/cameras', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(cameraData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إضافة الكاميرا بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addCameraModal')).hide();
            refreshCameraList();
        } else {
            showAlert(data.message || 'فشل في إضافة الكاميرا', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'error');
    });
}

// اختبار اتصال الكاميرا
function testCameraConnection() {
    const cameraData = {
        type: document.getElementById('cameraType').value,
        url: document.getElementById('cameraUrl').value,
        username: document.getElementById('cameraUsername').value,
        password: document.getElementById('cameraPassword').value
    };
    
    if (!cameraData.type || !cameraData.url) {
        showAlert('يرجى ملء نوع الكاميرا والعنوان أولاً', 'warning');
        return;
    }
    
    showAlert('جاري اختبار الاتصال...', 'info');
    
    fetch('/api/cameras/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(cameraData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم الاتصال بنجاح! ✅', 'success');
        } else {
            showAlert(`فشل الاتصال: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في اختبار الاتصال', 'error');
    });
}

// تحديث قائمة الكاميرات
function refreshCameraList() {
    location.reload();
}

// تشغيل كاميرا
function connectCamera(cameraId) {
    socket.emit('connect_camera', {camera_id: cameraId});
    showAlert(`جاري تشغيل الكاميرا ${cameraId}...`, 'info');
}

// إيقاف كاميرا
function disconnectCamera(cameraId) {
    socket.emit('disconnect_camera', {camera_id: cameraId});
    showAlert(`تم إيقاف الكاميرا ${cameraId}`, 'warning');
}

// اختبار كاميرا
function testCamera(cameraId) {
    socket.emit('test_camera', {camera_id: cameraId});
    showAlert(`جاري اختبار الكاميرا ${cameraId}...`, 'info');
}

// حذف كاميرا
function deleteCamera(cameraId) {
    if (confirm('هل أنت متأكد من حذف هذه الكاميرا؟')) {
        fetch(`/api/cameras/${cameraId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف الكاميرا بنجاح', 'success');
                refreshCameraList();
            } else {
                showAlert(data.message || 'فشل في حذف الكاميرا', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في الحذف', 'error');
        });
    }
}

// استقبال حالة الكاميرات من Socket.IO
socket.on('camera_status', function(data) {
    updateCameraStatus(data.camera_id, data.status);
});

// تحديث حالة الكاميرا في الجدول
function updateCameraStatus(cameraId, status) {
    const statusElement = document.getElementById(`status-${cameraId}`);
    const lastSeenElement = document.getElementById(`lastSeen-${cameraId}`);
    
    if (statusElement) {
        const statusConfig = {
            'online': { class: 'bg-success', text: 'متصل', icon: 'fa-circle' },
            'offline': { class: 'bg-danger', text: 'غير متصل', icon: 'fa-times-circle' },
            'connecting': { class: 'bg-warning', text: 'جاري الاتصال', icon: 'fa-spinner fa-spin' },
            'error': { class: 'bg-danger', text: 'خطأ', icon: 'fa-exclamation-triangle' }
        };
        
        const config = statusConfig[status] || statusConfig['offline'];
        statusElement.className = `badge ${config.class} status-badge`;
        statusElement.innerHTML = `<i class="fas ${config.icon} me-1"></i>${config.text}`;
    }
    
    if (lastSeenElement && status === 'online') {
        lastSeenElement.textContent = new Date().toLocaleTimeString('ar-SA');
    }
    
    updateStatistics();
}

// تحديث الإحصائيات
function updateStatistics() {
    const statusElements = document.querySelectorAll('.status-badge');
    let online = 0, offline = 0, recording = 0;
    
    statusElements.forEach(el => {
        if (el.classList.contains('bg-success')) online++;
        else if (el.classList.contains('bg-danger')) offline++;
    });
    
    document.getElementById('onlineCameras').textContent = online;
    document.getElementById('offlineCameras').textContent = offline;
    // recording count يحتاج API منفصل
}

// عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // طلب حالة جميع الكاميرات
    socket.emit('get_cameras_status');
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStatistics, 30000);
});
</script>
{% endblock %}
