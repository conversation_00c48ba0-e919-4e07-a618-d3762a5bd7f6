{% extends "base.html" %}

{% block title %}إدارة الكاميرات - نظام المراقبة الذكي{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-video me-2"></i>
                    إدارة الكاميرات
                </h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة كاميرا جديدة
                </button>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ cameras|length }}</div>
                        <div class="stats-label">إجمالي الكاميرات</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-video fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="onlineCameras">0</div>
                        <div class="stats-label">متصلة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-wifi fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="recordingCameras">0</div>
                        <div class="stats-label">تسجل الآن</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-record-vinyl fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="offlineCameras">0</div>
                        <div class="stats-label">غير متصلة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أزرار التحكم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-success" onclick="connectAllCameras()">
                            <i class="fas fa-play me-1"></i>
                            تشغيل الكل
                        </button>
                        <button type="button" class="btn btn-danger" onclick="disconnectAllCameras()">
                            <i class="fas fa-stop me-1"></i>
                            إيقاف الكل
                        </button>
                    </div>

                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-primary" onclick="refreshCameraList()">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث القائمة
                        </button>
                        <button type="button" class="btn btn-info" onclick="testAllConnections()">
                            <i class="fas fa-network-wired me-1"></i>
                            اختبار الاتصال
                        </button>
                    </div>

                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-success" onclick="autoDiscoverCameras()">
                            <i class="fas fa-search me-1"></i>
                            اكتشاف تلقائي
                        </button>
                        <button type="button" class="btn btn-info" onclick="scanNetwork()">
                            <i class="fas fa-wifi me-1"></i>
                            فحص الشبكة
                        </button>
                    </div>

                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-warning" onclick="exportCameraSettings()">
                            <i class="fas fa-download me-1"></i>
                            تصدير الإعدادات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="importCameraSettings()">
                            <i class="fas fa-upload me-1"></i>
                            استيراد الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- قائمة الكاميرات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الكاميرات
                    </h5>
                </div>
                <div class="card-body">
                    {% if cameras %}
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>العنوان</th>
                                    <th>الحالة</th>
                                    <th>آخر اتصال</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="camerasTableBody">
                                {% for camera in cameras %}
                                <tr data-camera-id="{{ camera.id }}">
                                    <td>{{ camera.id }}</td>
                                    <td>
                                        <strong>{{ camera.name }}</strong>
                                        {% if not camera.is_active %}
                                        <span class="badge bg-secondary ms-1">معطل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ camera.type.upper() }}</span>
                                    </td>
                                    <td>
                                        <code class="text-muted">{{ camera.url[:50] }}{% if camera.url|length > 50 %}...{% endif %}</code>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary status-badge" id="status-{{ camera.id }}">
                                            <i class="fas fa-circle me-1"></i>
                                            غير معروف
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted" id="lastSeen-{{ camera.id }}">-</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-success" onclick="connectCamera({{ camera.id }})" title="تشغيل">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="disconnectCamera({{ camera.id }})" title="إيقاف">
                                                <i class="fas fa-stop"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="testCamera({{ camera.id }})" title="اختبار">
                                                <i class="fas fa-network-wired"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="editCamera({{ camera.id }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteCamera({{ camera.id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-video-slash fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد كاميرات مضافة</h4>
                        <p class="text-muted">ابدأ بإضافة كاميرا جديدة لبدء المراقبة</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة كاميرا الآن
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة كاميرا -->
<div class="modal fade" id="addCameraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة كاميرا جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraName" class="form-label">اسم الكاميرا *</label>
                                <input type="text" class="form-control" id="cameraName" required
                                       placeholder="مثال: كاميرا المدخل الرئيسي">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraType" class="form-label">نوع الكاميرا *</label>
                                <select class="form-select" id="cameraType" required onchange="updateUrlPlaceholder()">
                                    <option value="">اختر نوع الكاميرا</option>
                                    <option value="rtsp">RTSP (IP Camera)</option>
                                    <option value="http">HTTP (MJPEG)</option>
                                    <option value="usb">USB Camera</option>
                                    <option value="onvif">ONVIF</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="cameraUrl" class="form-label">عنوان الكاميرا *</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="cameraUrl" required
                                   placeholder="سيتم تحديثه حسب نوع الكاميرا">
                            <button class="btn btn-outline-secondary" type="button" onclick="generateUrl()">
                                <i class="fas fa-magic"></i>
                                توليد تلقائي
                            </button>
                            <button class="btn btn-outline-info" type="button" onclick="detectChannels()">
                                <i class="fas fa-search"></i>
                                اكتشاف القنوات
                            </button>
                        </div>
                        <div class="form-text" id="urlHelp">
                            أمثلة على العناوين ستظهر هنا حسب النوع المختار
                        </div>

                        <!-- قائمة القنوات المكتشفة -->
                        <div id="channelsContainer" style="display: none;" class="mt-2">
                            <label class="form-label">القنوات المكتشفة:</label>
                            <div id="channelsList" class="border rounded p-2 bg-dark">
                                <!-- ستتم إضافة القنوات هنا -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="row" id="credentialsSection" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraUsername" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="cameraUsername"
                                       placeholder="admin">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraPassword" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="cameraPassword"
                                       placeholder="كلمة المرور">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraResolution" class="form-label">الدقة</label>
                                <select class="form-select" id="cameraResolution">
                                    <option value="1920x1080">1080p (1920×1080)</option>
                                    <option value="1280x720">720p (1280×720)</option>
                                    <option value="640x480">480p (640×480)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraFps" class="form-label">معدل الإطارات</label>
                                <select class="form-select" id="cameraFps">
                                    <option value="30">30 FPS</option>
                                    <option value="25" selected>25 FPS</option>
                                    <option value="15">15 FPS</option>
                                    <option value="10">10 FPS</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إعدادات متقدمة -->
                    <div class="mb-3">
                        <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#advancedSettings">
                            <i class="fas fa-cog me-1"></i>
                            إعدادات متقدمة
                        </button>
                    </div>

                    <div class="collapse" id="advancedSettings">
                        <div class="card card-body bg-secondary">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="cameraTimeout" class="form-label">مهلة الاتصال (ثانية)</label>
                                        <input type="number" class="form-control" id="cameraTimeout" value="10" min="5" max="60">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="cameraRetries" class="form-label">عدد المحاولات</label>
                                        <input type="number" class="form-control" id="cameraRetries" value="3" min="1" max="10">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="cameraProtocol" class="form-label">بروتوكول النقل</label>
                                        <select class="form-select" id="cameraProtocol">
                                            <option value="tcp">TCP</option>
                                            <option value="udp">UDP</option>
                                            <option value="http">HTTP</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="cameraBuffer" class="form-label">حجم المخزن المؤقت</label>
                                        <select class="form-select" id="cameraBuffer">
                                            <option value="1">1 إطار</option>
                                            <option value="2">2 إطار</option>
                                            <option value="3">3 إطار</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="cameraAutoReconnect" checked>
                                    <label class="form-check-label" for="cameraAutoReconnect">
                                        إعادة الاتصال التلقائي
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="cameraMotionDetection">
                                    <label class="form-check-label" for="cameraMotionDetection">
                                        تفعيل كشف الحركة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cameraActive" checked>
                            <label class="form-check-label" for="cameraActive">
                                تفعيل الكاميرا فور الإضافة
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="testCameraConnection()">
                    <i class="fas fa-network-wired me-1"></i>
                    اختبار الاتصال
                </button>
                <button type="button" class="btn btn-primary" onclick="addCamera()">
                    <i class="fas fa-plus me-1"></i>
                    إضافة الكاميرا
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة اكتشاف الكاميرات -->
<div class="modal fade" id="autoDiscoveryModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>
                    اكتشاف الكاميرات التلقائي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- شريط التقدم -->
                <div id="discoveryProgress" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>جاري فحص الشبكة...</span>
                        <span id="progressText">0%</span>
                    </div>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             id="progressBar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- إعدادات الفحص -->
                <div id="discoverySettings">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="networkRange" class="form-label">نطاق الشبكة</label>
                            <input type="text" class="form-control" id="networkRange"
                                   value="***********-254" placeholder="***********-254">
                        </div>
                        <div class="col-md-6">
                            <label for="portRange" class="form-label">نطاق المنافذ</label>
                            <input type="text" class="form-control" id="portRange"
                                   value="554,8080,80,8000" placeholder="554,8080,80,8000">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="scanRTSP" checked>
                                <label class="form-check-label" for="scanRTSP">RTSP</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="scanHTTP" checked>
                                <label class="form-check-label" for="scanHTTP">HTTP</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="scanONVIF" checked>
                                <label class="form-check-label" for="scanONVIF">ONVIF</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نتائج الاكتشاف -->
                <div id="discoveryResults" style="display: none;">
                    <h6>الكاميرات المكتشفة:</h6>
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>اختيار</th>
                                    <th>العنوان</th>
                                    <th>النوع</th>
                                    <th>الحالة</th>
                                    <th>المعلومات</th>
                                </tr>
                            </thead>
                            <tbody id="discoveredCameras">
                                <!-- ستتم إضافة النتائج هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="startDiscovery" onclick="startAutoDiscovery()">
                    <i class="fas fa-search me-1"></i>
                    بدء الفحص
                </button>
                <button type="button" class="btn btn-success" id="addSelectedCameras"
                        onclick="addSelectedCameras()" style="display: none;">
                    <i class="fas fa-plus me-1"></i>
                    إضافة المحددة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تعديل الكاميرا -->
<div class="modal fade" id="editCameraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الكاميرا
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- نفس النموذج مع بيانات محملة -->
                <form id="editCameraForm">
                    <input type="hidden" id="editCameraId">
                    <!-- باقي الحقول مثل نموذج الإضافة -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="updateCamera()">
                    <i class="fas fa-save me-1"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.status-badge.bg-success { animation: pulse 2s infinite; }
.status-badge.bg-danger { animation: none; }

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.table td {
    vertical-align: middle;
}

.btn-group-sm .btn {
    margin: 0 1px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let socket = io();

// تحديث placeholder حسب نوع الكاميرا
function updateUrlPlaceholder() {
    const type = document.getElementById('cameraType').value;
    const urlInput = document.getElementById('cameraUrl');
    const helpText = document.getElementById('urlHelp');
    const credentialsSection = document.getElementById('credentialsSection');

    const examples = {
        'rtsp': {
            placeholder: 'rtsp://***********00:554/stream1',
            help: 'مثال: rtsp://***********00:554/stream1 أو rtsp://admin:password@***********00:554/cam/realmonitor?channel=1&subtype=0',
            showCredentials: true
        },
        'http': {
            placeholder: 'http://***********00/mjpeg',
            help: 'مثال: http://***********00/mjpeg أو http://***********00/video.cgi',
            showCredentials: true
        },
        'usb': {
            placeholder: '0',
            help: 'رقم الكاميرا: 0 للكاميرا الأولى، 1 للثانية، إلخ',
            showCredentials: false
        },
        'onvif': {
            placeholder: 'http://***********00:80/onvif/device_service',
            help: 'مثال: http://***********00:80/onvif/device_service',
            showCredentials: true
        }
    };

    if (examples[type]) {
        urlInput.placeholder = examples[type].placeholder;
        helpText.textContent = examples[type].help;
        credentialsSection.style.display = examples[type].showCredentials ? 'block' : 'none';
    }
}

// توليد URL تلقائي
function generateUrl() {
    const type = document.getElementById('cameraType').value;
    const urlInput = document.getElementById('cameraUrl');

    if (!type) {
        showAlert('يرجى اختيار نوع الكاميرا أولاً', 'warning');
        return;
    }

    // الحصول على عنوان IP المحلي
    const localIP = getLocalIP();

    const templates = {
        'rtsp': `rtsp://${localIP}:554/stream1`,
        'http': `http://${localIP}/mjpeg`,
        'onvif': `http://${localIP}:80/onvif/device_service`,
        'usb': '0'
    };

    if (templates[type]) {
        urlInput.value = templates[type];
        showAlert('تم توليد العنوان تلقائياً', 'success');
    }
}

// الحصول على عنوان IP المحلي (تقريبي)
function getLocalIP() {
    // هذا مجرد تخمين، في التطبيق الحقيقي يجب الحصول عليه من الخادم
    return '***********00';
}

// اكتشاف القنوات
function detectChannels() {
    const type = document.getElementById('cameraType').value;
    const url = document.getElementById('cameraUrl').value;
    const username = document.getElementById('cameraUsername').value;
    const password = document.getElementById('cameraPassword').value;

    if (!type || !url) {
        showAlert('يرجى ملء نوع الكاميرا والعنوان أولاً', 'warning');
        return;
    }

    showAlert('جاري اكتشاف القنوات...', 'info');

    fetch('/api/cameras/detect-channels', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: type,
            url: url,
            username: username,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.channels.length > 0) {
            displayChannels(data.channels);
            showAlert(`تم اكتشاف ${data.channels.length} قناة`, 'success');
        } else {
            showAlert('لم يتم العثور على قنوات', 'warning');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في اكتشاف القنوات', 'error');
    });
}

// عرض القنوات المكتشفة
function displayChannels(channels) {
    const container = document.getElementById('channelsContainer');
    const channelsList = document.getElementById('channelsList');

    channelsList.innerHTML = '';

    channels.forEach((channel, index) => {
        const channelDiv = document.createElement('div');
        channelDiv.className = 'form-check mb-2';
        channelDiv.innerHTML = `
            <input class="form-check-input" type="radio" name="selectedChannel"
                   id="channel${index}" value="${channel.url}">
            <label class="form-check-label" for="channel${index}">
                <strong>قناة ${channel.number}</strong> - ${channel.name || 'غير محدد'}
                <br><small class="text-muted">${channel.url}</small>
                ${channel.resolution ? `<br><small class="text-info">${channel.resolution}</small>` : ''}
            </label>
        `;
        channelsList.appendChild(channelDiv);
    });

    container.style.display = 'block';

    // إضافة مستمع للاختيار
    document.querySelectorAll('input[name="selectedChannel"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('cameraUrl').value = this.value;
            }
        });
    });
}

// إضافة كاميرا جديدة
function addCamera() {
    const form = document.getElementById('addCameraForm');
    const formData = new FormData(form);
    
    const cameraData = {
        name: document.getElementById('cameraName').value,
        type: document.getElementById('cameraType').value,
        url: document.getElementById('cameraUrl').value,
        username: document.getElementById('cameraUsername').value,
        password: document.getElementById('cameraPassword').value,
        resolution: document.getElementById('cameraResolution').value,
        fps: document.getElementById('cameraFps').value,
        is_active: document.getElementById('cameraActive').checked
    };
    
    // التحقق من البيانات
    if (!cameraData.name || !cameraData.type || !cameraData.url) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // إرسال البيانات
    fetch('/api/cameras', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(cameraData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إضافة الكاميرا بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addCameraModal')).hide();
            refreshCameraList();
        } else {
            showAlert(data.message || 'فشل في إضافة الكاميرا', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'error');
    });
}

// اختبار اتصال الكاميرا
function testCameraConnection() {
    const cameraData = {
        type: document.getElementById('cameraType').value,
        url: document.getElementById('cameraUrl').value,
        username: document.getElementById('cameraUsername').value,
        password: document.getElementById('cameraPassword').value
    };
    
    if (!cameraData.type || !cameraData.url) {
        showAlert('يرجى ملء نوع الكاميرا والعنوان أولاً', 'warning');
        return;
    }
    
    showAlert('جاري اختبار الاتصال...', 'info');
    
    fetch('/api/cameras/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(cameraData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم الاتصال بنجاح! ✅', 'success');
        } else {
            showAlert(`فشل الاتصال: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في اختبار الاتصال', 'error');
    });
}

// تحديث قائمة الكاميرات
function refreshCameraList() {
    location.reload();
}

// تشغيل كاميرا
function connectCamera(cameraId) {
    socket.emit('connect_camera', {camera_id: cameraId});
    showAlert(`جاري تشغيل الكاميرا ${cameraId}...`, 'info');
}

// إيقاف كاميرا
function disconnectCamera(cameraId) {
    socket.emit('disconnect_camera', {camera_id: cameraId});
    showAlert(`تم إيقاف الكاميرا ${cameraId}`, 'warning');
}

// اختبار كاميرا
function testCamera(cameraId) {
    socket.emit('test_camera', {camera_id: cameraId});
    showAlert(`جاري اختبار الكاميرا ${cameraId}...`, 'info');
}

// حذف كاميرا
function deleteCamera(cameraId) {
    if (confirm('هل أنت متأكد من حذف هذه الكاميرا؟')) {
        fetch(`/api/cameras/${cameraId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف الكاميرا بنجاح', 'success');
                refreshCameraList();
            } else {
                showAlert(data.message || 'فشل في حذف الكاميرا', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في الحذف', 'error');
        });
    }
}

// استقبال حالة الكاميرات من Socket.IO
socket.on('camera_status', function(data) {
    updateCameraStatus(data.camera_id, data.status);
});

// تحديث حالة الكاميرا في الجدول
function updateCameraStatus(cameraId, status) {
    const statusElement = document.getElementById(`status-${cameraId}`);
    const lastSeenElement = document.getElementById(`lastSeen-${cameraId}`);
    
    if (statusElement) {
        const statusConfig = {
            'online': { class: 'bg-success', text: 'متصل', icon: 'fa-circle' },
            'offline': { class: 'bg-danger', text: 'غير متصل', icon: 'fa-times-circle' },
            'connecting': { class: 'bg-warning', text: 'جاري الاتصال', icon: 'fa-spinner fa-spin' },
            'error': { class: 'bg-danger', text: 'خطأ', icon: 'fa-exclamation-triangle' }
        };
        
        const config = statusConfig[status] || statusConfig['offline'];
        statusElement.className = `badge ${config.class} status-badge`;
        statusElement.innerHTML = `<i class="fas ${config.icon} me-1"></i>${config.text}`;
    }
    
    if (lastSeenElement && status === 'online') {
        lastSeenElement.textContent = new Date().toLocaleTimeString('ar-SA');
    }
    
    updateStatistics();
}

// الاكتشاف التلقائي للكاميرات
function autoDiscoverCameras() {
    const modal = new bootstrap.Modal(document.getElementById('autoDiscoveryModal'));
    modal.show();
}

// فحص الشبكة
function scanNetwork() {
    showAlert('جاري فحص الشبكة المحلية...', 'info');

    fetch('/api/cameras/scan-network', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`تم العثور على ${data.devices.length} جهاز في الشبكة`, 'success');
            displayNetworkDevices(data.devices);
        } else {
            showAlert('لم يتم العثور على أجهزة', 'warning');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في فحص الشبكة', 'error');
    });
}

// بدء الاكتشاف التلقائي
function startAutoDiscovery() {
    const networkRange = document.getElementById('networkRange').value;
    const portRange = document.getElementById('portRange').value;
    const scanRTSP = document.getElementById('scanRTSP').checked;
    const scanHTTP = document.getElementById('scanHTTP').checked;
    const scanONVIF = document.getElementById('scanONVIF').checked;

    if (!networkRange || !portRange) {
        showAlert('يرجى ملء نطاق الشبكة والمنافذ', 'warning');
        return;
    }

    // إخفاء الإعدادات وإظهار شريط التقدم
    document.getElementById('discoverySettings').style.display = 'none';
    document.getElementById('discoveryProgress').style.display = 'block';
    document.getElementById('startDiscovery').style.display = 'none';

    const discoveryData = {
        network_range: networkRange,
        port_range: portRange,
        protocols: {
            rtsp: scanRTSP,
            http: scanHTTP,
            onvif: scanONVIF
        }
    };

    // بدء الفحص
    fetch('/api/cameras/auto-discover', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(discoveryData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // محاكاة شريط التقدم
            simulateProgress(() => {
                displayDiscoveryResults(data.cameras);
            });
        } else {
            showAlert(data.message || 'فشل في الاكتشاف', 'error');
            resetDiscoveryModal();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاكتشاف', 'error');
        resetDiscoveryModal();
    });
}

// محاكاة شريط التقدم
function simulateProgress(callback) {
    let progress = 0;
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;

        progressBar.style.width = progress + '%';
        progressText.textContent = Math.round(progress) + '%';

        if (progress >= 100) {
            clearInterval(interval);
            setTimeout(callback, 500);
        }
    }, 200);
}

// عرض نتائج الاكتشاف
function displayDiscoveryResults(cameras) {
    const resultsDiv = document.getElementById('discoveryResults');
    const camerasTable = document.getElementById('discoveredCameras');

    camerasTable.innerHTML = '';

    if (cameras.length === 0) {
        camerasTable.innerHTML = `
            <tr>
                <td colspan="5" class="text-center text-muted">
                    لم يتم العثور على كاميرات
                </td>
            </tr>
        `;
    } else {
        cameras.forEach((camera, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox"
                               id="camera${index}" value="${index}">
                    </div>
                </td>
                <td>
                    <code>${camera.ip}:${camera.port}</code>
                    <br><small class="text-muted">${camera.url}</small>
                </td>
                <td>
                    <span class="badge bg-info">${camera.type.toUpperCase()}</span>
                </td>
                <td>
                    <span class="badge ${camera.status === 'online' ? 'bg-success' : 'bg-warning'}">
                        ${camera.status === 'online' ? 'متاح' : 'يحتاج اختبار'}
                    </span>
                </td>
                <td>
                    ${camera.manufacturer || 'غير محدد'}
                    ${camera.model ? `<br><small>${camera.model}</small>` : ''}
                </td>
            `;
            camerasTable.appendChild(row);
        });

        document.getElementById('addSelectedCameras').style.display = 'inline-block';
    }

    document.getElementById('discoveryProgress').style.display = 'none';
    resultsDiv.style.display = 'block';
}

// إضافة الكاميرات المحددة
function addSelectedCameras() {
    const checkboxes = document.querySelectorAll('#discoveredCameras input[type="checkbox"]:checked');

    if (checkboxes.length === 0) {
        showAlert('يرجى اختيار كاميرا واحدة على الأقل', 'warning');
        return;
    }

    const selectedCameras = Array.from(checkboxes).map(cb => {
        const index = parseInt(cb.value);
        // هنا يجب الحصول على بيانات الكاميرا من المصفوفة المحفوظة
        return {
            name: `كاميرا مكتشفة ${index + 1}`,
            url: cb.closest('tr').querySelector('code').textContent,
            type: 'rtsp' // أو حسب النوع المكتشف
        };
    });

    // إضافة الكاميرات
    selectedCameras.forEach(camera => {
        addCameraFromDiscovery(camera);
    });

    bootstrap.Modal.getInstance(document.getElementById('autoDiscoveryModal')).hide();
    showAlert(`تم إضافة ${selectedCameras.length} كاميرا`, 'success');
    refreshCameraList();
}

// إضافة كاميرا من الاكتشاف
function addCameraFromDiscovery(cameraData) {
    fetch('/api/cameras', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(cameraData)
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('فشل في إضافة الكاميرا:', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// إعادة تعيين نافذة الاكتشاف
function resetDiscoveryModal() {
    document.getElementById('discoverySettings').style.display = 'block';
    document.getElementById('discoveryProgress').style.display = 'none';
    document.getElementById('discoveryResults').style.display = 'none';
    document.getElementById('startDiscovery').style.display = 'inline-block';
    document.getElementById('addSelectedCameras').style.display = 'none';
}

// تحديث الإحصائيات
function updateStatistics() {
    const statusElements = document.querySelectorAll('.status-badge');
    let online = 0, offline = 0, recording = 0;

    statusElements.forEach(el => {
        if (el.classList.contains('bg-success')) online++;
        else if (el.classList.contains('bg-danger')) offline++;
    });

    document.getElementById('onlineCameras').textContent = online;
    document.getElementById('offlineCameras').textContent = offline;
    // recording count يحتاج API منفصل
}

// عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // طلب حالة جميع الكاميرات
    socket.emit('get_cameras_status');
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStatistics, 30000);
});
</script>
{% endblock %}
