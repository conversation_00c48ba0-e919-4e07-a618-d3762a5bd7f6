#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لنظام المراقبة الذكي (بدون مكتبات إضافية)
Quick Start for Smart Surveillance System (without additional libraries)
"""

import os
import sys
import json
import logging
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler
import webbrowser
import threading
import time

class SmartSurveillanceHandler(SimpleHTTPRequestHandler):
    """معالج HTTP مخصص لنظام المراقبة"""
    
    def do_GET(self):
        """معالجة طلبات GET"""
        if self.path == '/':
            self.send_demo_page()
        elif self.path == '/api/status':
            self.send_status_api()
        else:
            super().do_GET()
    
    def send_demo_page(self):
        """إرسال صفحة تجريبية"""
        html_content = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المراقبة الذكي - تجريبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1a1a1a, #2c3e50);
            color: #ffffff;
            min-height: 100vh;
        }
        .hero-section {
            padding: 100px 0;
            text-align: center;
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-10px);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
            animation: pulse 2s infinite;
        }
        .status-online { background-color: #27ae60; }
        .status-offline { background-color: #e74c3c; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 40px 0;
        }
        .camera-placeholder {
            aspect-ratio: 16/9;
            background: linear-gradient(45deg, #34495e, #2c3e50);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- القسم الرئيسي -->
        <div class="hero-section">
            <h1 class="display-4 mb-4">
                <i class="fas fa-video me-3"></i>
                نظام المراقبة الذكي
            </h1>
            <p class="lead mb-4">مشابه لجهاز Dahua DHI-XVR5116H</p>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                النظام يعمل بنجاح! 
                <span class="status-indicator status-online"></span>
                متصل
            </div>
        </div>
        
        <!-- الميزات -->
        <div class="row">
            <div class="col-md-4">
                <div class="feature-card">
                    <i class="fas fa-video fa-3x mb-3 text-primary"></i>
                    <h4>بث مباشر</h4>
                    <p>دعم حتى 16 كاميرا متزامنة بجودة عالية</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <i class="fas fa-brain fa-3x mb-3 text-success"></i>
                    <h4>ذكاء اصطناعي</h4>
                    <p>تعرف على الوجوه وكشف الحركة الذكي</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <i class="fas fa-record-vinyl fa-3x mb-3 text-warning"></i>
                    <h4>تسجيل تلقائي</h4>
                    <p>تسجيل مجدول وعند كشف الحركة</p>
                </div>
            </div>
        </div>
        
        <!-- شبكة الكاميرات التجريبية -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="text-center mb-4">
                    <i class="fas fa-th me-2"></i>
                    شبكة الكاميرات (تجريبي)
                </h3>
                <div class="demo-grid">
                    <div class="camera-placeholder">
                        <div class="text-center">
                            <i class="fas fa-video fa-3x mb-2"></i>
                            <p>كاميرا المدخل الرئيسي</p>
                            <small class="text-success">
                                <span class="status-indicator status-online"></span>
                                متصل
                            </small>
                        </div>
                    </div>
                    <div class="camera-placeholder">
                        <div class="text-center">
                            <i class="fas fa-video fa-3x mb-2"></i>
                            <p>كاميرا الموقف</p>
                            <small class="text-success">
                                <span class="status-indicator status-online"></span>
                                متصل
                            </small>
                        </div>
                    </div>
                    <div class="camera-placeholder">
                        <div class="text-center">
                            <i class="fas fa-video fa-3x mb-2"></i>
                            <p>كاميرا الحديقة</p>
                            <small class="text-danger">
                                <span class="status-indicator status-offline"></span>
                                غير متصل
                            </small>
                        </div>
                    </div>
                    <div class="camera-placeholder">
                        <div class="text-center">
                            <i class="fas fa-video fa-3x mb-2"></i>
                            <p>كاميرا المكتب</p>
                            <small class="text-success">
                                <span class="status-indicator status-online"></span>
                                متصل
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- معلومات النظام -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card bg-dark">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>الإصدار:</strong> 1.0.0</p>
                                <p><strong>حالة النظام:</strong> <span class="text-success">يعمل</span></p>
                                <p><strong>وقت التشغيل:</strong> <span id="uptime">00:00:00</span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>الكاميرات النشطة:</strong> 3/4</p>
                                <p><strong>التسجيلات اليوم:</strong> 12</p>
                                <p><strong>آخر حدث:</strong> كشف حركة - 14:30</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أزرار التحكم -->
        <div class="row mt-4 mb-5">
            <div class="col-12 text-center">
                <h4 class="mb-3">للحصول على النظام الكامل:</h4>
                <div class="btn-group" role="group">
                    <button class="btn btn-primary btn-lg" onclick="showFullSystemInfo()">
                        <i class="fas fa-rocket me-2"></i>
                        تشغيل النظام الكامل
                    </button>
                    <button class="btn btn-success btn-lg" onclick="showInstallGuide()">
                        <i class="fas fa-download me-2"></i>
                        دليل التثبيت
                    </button>
                    <button class="btn btn-info btn-lg" onclick="showDocumentation()">
                        <i class="fas fa-book me-2"></i>
                        الوثائق
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal للمعلومات -->
    <div class="modal fade" id="infoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">معلومات</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- المحتوى سيتم إدراجه بواسطة JavaScript -->
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث وقت التشغيل
        let startTime = Date.now();
        function updateUptime() {
            const elapsed = Date.now() - startTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('uptime').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        setInterval(updateUptime, 1000);
        
        // وظائف الأزرار
        function showFullSystemInfo() {
            document.getElementById('modalTitle').textContent = 'تشغيل النظام الكامل';
            document.getElementById('modalBody').innerHTML = `
                <h6>لتشغيل النظام الكامل مع جميع الميزات:</h6>
                <ol>
                    <li>تأكد من تثبيت جميع المكتبات: <code>pip install -r requirements.txt</code></li>
                    <li>شغل النظام: <code>python run.py</code></li>
                    <li>أو استخدم: <code>start.bat</code> في Windows</li>
                </ol>
                <div class="alert alert-info">
                    <strong>بيانات الدخول الافتراضية:</strong><br>
                    اسم المستخدم: admin<br>
                    كلمة المرور: admin123
                </div>
            `;
            new bootstrap.Modal(document.getElementById('infoModal')).show();
        }
        
        function showInstallGuide() {
            document.getElementById('modalTitle').textContent = 'دليل التثبيت';
            document.getElementById('modalBody').innerHTML = `
                <h6>متطلبات النظام:</h6>
                <ul>
                    <li>Python 3.8 أو أحدث</li>
                    <li>كاميرا ويب أو كاميرات IP</li>
                    <li>4GB RAM كحد أدنى</li>
                </ul>
                <h6>خطوات التثبيت:</h6>
                <ol>
                    <li>تحميل المشروع</li>
                    <li>تثبيت المكتبات: <code>pip install -r requirements.txt</code></li>
                    <li>تشغيل الاختبار: <code>python test_system.py</code></li>
                    <li>تشغيل النظام: <code>python run.py</code></li>
                </ol>
            `;
            new bootstrap.Modal(document.getElementById('infoModal')).show();
        }
        
        function showDocumentation() {
            document.getElementById('modalTitle').textContent = 'الوثائق';
            document.getElementById('modalBody').innerHTML = `
                <h6>الملفات المهمة:</h6>
                <ul>
                    <li><strong>README.md</strong> - دليل شامل للنظام</li>
                    <li><strong>config.json</strong> - ملف الإعدادات</li>
                    <li><strong>app.py</strong> - التطبيق الرئيسي</li>
                    <li><strong>test_system.py</strong> - اختبار النظام</li>
                </ul>
                <h6>المجلدات:</h6>
                <ul>
                    <li><strong>templates/</strong> - قوالب HTML</li>
                    <li><strong>stream/</strong> - وحدة البث</li>
                    <li><strong>ai_modules/</strong> - الذكاء الاصطناعي</li>
                    <li><strong>recordings/</strong> - التسجيلات</li>
                </ul>
            `;
            new bootstrap.Modal(document.getElementById('infoModal')).show();
        }
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def send_status_api(self):
        """إرسال API حالة النظام"""
        status_data = {
            "status": "running",
            "version": "1.0.0",
            "uptime": "00:15:30",
            "cameras": {
                "total": 4,
                "online": 3,
                "offline": 1
            },
            "recordings_today": 12,
            "last_event": "Motion detected at 14:30"
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(status_data, ensure_ascii=False).encode('utf-8'))

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║           🎥 نظام المراقبة الذكي 🎥                          ║
    ║              Smart Surveillance System                       ║
    ║                                                              ║
    ║                    تشغيل سريع - Demo Mode                    ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def open_browser_delayed(url, delay=2):
    """فتح المتصفح بعد تأخير"""
    time.sleep(delay)
    try:
        webbrowser.open(url)
        print(f"🌐 تم فتح المتصفح: {url}")
    except Exception as e:
        print(f"⚠️  لا يمكن فتح المتصفح تلقائياً: {e}")
        print(f"يرجى فتح الرابط يدوياً: {url}")

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    # إعدادات الخادم
    host = 'localhost'
    port = 8080
    
    print(f"🚀 بدء تشغيل الخادم التجريبي...")
    print(f"🌐 العنوان: http://{host}:{port}")
    print(f"⏹️  اضغط Ctrl+C لإيقاف الخادم")
    print()
    
    try:
        # إنشاء الخادم
        server = HTTPServer((host, port), SmartSurveillanceHandler)
        
        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(
            target=open_browser_delayed, 
            args=(f"http://{host}:{port}", 2),
            daemon=True
        )
        browser_thread.start()
        
        print("✅ الخادم يعمل...")
        print("📱 يمكنك الوصول للنظام من أي جهاز على نفس الشبكة")
        print()
        
        # تشغيل الخادم
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف الخادم بواسطة المستخدم")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ المنفذ {port} مستخدم بالفعل")
            print("جرب منفذ آخر أو أوقف التطبيق الذي يستخدم هذا المنفذ")
        else:
            print(f"❌ خطأ في تشغيل الخادم: {e}")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
    
    print("\n✅ تم إغلاق النظام التجريبي")

if __name__ == "__main__":
    main()
