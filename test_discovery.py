#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وحدة اكتشاف الكاميرات
Test Camera Discovery Module
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.camera_discovery import CameraDiscovery
import time

def test_camera_discovery():
    """اختبار اكتشاف الكاميرات"""
    print("🔍 اختبار وحدة اكتشاف الكاميرات")
    print("="*50)
    
    discovery = CameraDiscovery()
    
    # اختبار 1: فحص نطاق محدود
    print("\n📡 اختبار 1: فحص نطاق محدود")
    print("جاري فحص ***********-10...")
    
    start_time = time.time()
    cameras = discovery.scan_network_range(
        "***********-10", 
        {'rtsp': True, 'http': True, 'onvif': False}
    )
    end_time = time.time()
    
    print(f"⏱️  وقت الفحص: {end_time - start_time:.2f} ثانية")
    print(f"📹 تم اكتشاف {len(cameras)} كاميرا")
    
    for i, camera in enumerate(cameras, 1):
        print(f"  {i}. {camera['type'].upper()}: {camera['ip']}:{camera['port']}")
        print(f"     URL: {camera['url']}")
        print(f"     الشركة: {camera['manufacturer']}")
        print()
    
    # اختبار 2: اكتشاف القنوات
    if cameras:
        print("\n📺 اختبار 2: اكتشاف القنوات")
        first_camera = cameras[0]
        print(f"اختبار الكاميرا: {first_camera['url']}")
        
        channels = discovery.detect_channels(
            first_camera['url'],
            first_camera.get('username', ''),
            first_camera.get('password', '')
        )
        
        print(f"📡 تم اكتشاف {len(channels)} قناة:")
        for channel in channels:
            print(f"  - قناة {channel['number']}: {channel['name']}")
            print(f"    URL: {channel['url']}")
            if channel['resolution']:
                print(f"    الدقة: {channel['resolution']}")
            print()
    
    # اختبار 3: فحص الشبكة المحلية
    print("\n🌐 اختبار 3: فحص الشبكة المحلية")
    print("جاري فحص الشبكة المحلية...")
    
    start_time = time.time()
    local_cameras = discovery.scan_local_network()
    end_time = time.time()
    
    print(f"⏱️  وقت الفحص: {end_time - start_time:.2f} ثانية")
    print(f"📹 تم اكتشاف {len(local_cameras)} كاميرا في الشبكة المحلية")
    
    # اختبار 4: اختبار منافذ مختلفة
    print("\n🔌 اختبار 4: فحص منافذ شائعة")
    test_ip = "***********"  # يمكن تغييرها
    
    common_ports = [554, 8080, 80, 8000, 8554]
    print(f"فحص المنافذ الشائعة على {test_ip}:")
    
    for port in common_ports:
        is_open = discovery._is_port_open(test_ip, port)
        status = "✅ مفتوح" if is_open else "❌ مغلق"
        print(f"  المنفذ {port}: {status}")
    
    print("\n" + "="*50)
    print("✅ انتهى اختبار وحدة اكتشاف الكاميرات")

def test_specific_camera():
    """اختبار كاميرا محددة"""
    print("\n🎯 اختبار كاميرا محددة")
    print("="*30)
    
    # يمكن تخصيص هذه القيم
    test_urls = [
        "rtsp://admin:admin@***********00:554/stream1",
        "http://***********00/mjpeg",
        "rtsp://***********00:554/cam/realmonitor?channel=1&subtype=0"
    ]
    
    discovery = CameraDiscovery()
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n{i}. اختبار: {url}")
        
        if url.startswith('rtsp'):
            success = discovery._test_rtsp_stream(url)
        else:
            success = discovery._test_http_stream(url)
        
        status = "✅ يعمل" if success else "❌ لا يعمل"
        print(f"   النتيجة: {status}")

def simulate_discovery_progress():
    """محاكاة تقدم الاكتشاف"""
    print("\n⏳ محاكاة تقدم الاكتشاف")
    print("="*30)
    
    import time
    
    steps = [
        "فحص المنافذ المفتوحة...",
        "اختبار بروتوكول RTSP...",
        "اختبار بروتوكول HTTP...",
        "اختبار ONVIF...",
        "اكتشاف القنوات...",
        "تحديد الشركة المصنعة...",
        "انتهى الفحص"
    ]
    
    for i, step in enumerate(steps):
        progress = (i + 1) / len(steps) * 100
        print(f"[{progress:5.1f}%] {step}")
        time.sleep(0.5)  # محاكاة الوقت
    
    print("✅ تم الانتهاء من الفحص")

def main():
    """الوظيفة الرئيسية"""
    print("🎥 نظام المراقبة الذكي - اختبار اكتشاف الكاميرات")
    print("Smart Surveillance System - Camera Discovery Test")
    print("="*60)
    
    try:
        # اختبار الوحدة
        test_camera_discovery()
        
        # اختبار كاميرا محددة
        test_specific_camera()
        
        # محاكاة التقدم
        simulate_discovery_progress()
        
        print("\n🎉 جميع الاختبارات مكتملة!")
        
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
