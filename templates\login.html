{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام المراقبة الذكي{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center align-items-center" style="min-height: 80vh;">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg">
                <div class="card-header text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        تسجيل الدخول
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST" id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                اسم المستخدم
                            </label>
                            <input type="text" class="form-control" id="username" name="username" required
                                   placeholder="أدخل اسم المستخدم">
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                كلمة المرور
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required
                                       placeholder="أدخل كلمة المرور">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                دخول
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="card-footer text-center text-muted">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        المستخدم الافتراضي: admin / admin123
                    </small>
                </div>
            </div>
            
            <!-- معلومات النظام -->
            <div class="card mt-4">
                <div class="card-header text-center">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stats-card bg-primary">
                                <div class="stats-number">16</div>
                                <div class="stats-label">قناة كحد أقصى</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stats-card bg-success">
                                <div class="stats-number">AI</div>
                                <div class="stats-label">ذكاء اصطناعي</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stats-card bg-warning">
                                <div class="stats-number">24/7</div>
                                <div class="stats-label">مراقبة مستمرة</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6 class="text-center mb-3">الميزات المتاحة:</h6>
                        <div class="row">
                            <div class="col-6">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>بث مباشر عالي الجودة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>كشف الحركة الذكي</li>
                                    <li><i class="fas fa-check text-success me-2"></i>التعرف على الوجوه</li>
                                    <li><i class="fas fa-check text-success me-2"></i>تسجيل تلقائي</li>
                                </ul>
                            </div>
                            <div class="col-6">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>دعم RTSP/IP</li>
                                    <li><i class="fas fa-check text-success me-2"></i>تنبيهات فورية</li>
                                    <li><i class="fas fa-check text-success me-2"></li>واجهة عربية</li>
                                    <li><i class="fas fa-check text-success me-2"></i>أمان متقدم</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .stats-card {
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 10px;
        transition: transform 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
    }
    
    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .stats-label {
        font-size: 0.8rem;
        opacity: 0.9;
    }
    
    .bg-primary {
        background: linear-gradient(135deg, #3498db, #2980b9) !important;
    }
    
    .bg-success {
        background: linear-gradient(135deg, #27ae60, #229954) !important;
    }
    
    .bg-warning {
        background: linear-gradient(135deg, #f39c12, #e67e22) !important;
    }
    
    .form-control:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }
    
    .input-group .btn-outline-secondary {
        border-color: #ced4da;
        color: #6c757d;
    }
    
    .input-group .btn-outline-secondary:hover {
        background-color: #3498db;
        border-color: #3498db;
        color: white;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تبديل إظهار/إخفاء كلمة المرور
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        icon.classList.toggle('fa-eye');
        icon.classList.toggle('fa-eye-slash');
    });
    
    // تحسين تجربة المستخدم
    const loginForm = document.getElementById('loginForm');
    const submitBtn = loginForm.querySelector('button[type="submit"]');
    
    loginForm.addEventListener('submit', function(e) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
        submitBtn.disabled = true;
    });
    
    // تركيز تلقائي على حقل اسم المستخدم
    document.getElementById('username').focus();
    
    // إضافة تأثيرات بصرية
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});

// إضافة تأثير الكتابة للعنوان
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// تشغيل تأثير الكتابة عند تحميل الصفحة
window.addEventListener('load', function() {
    const title = document.querySelector('.card-header h3');
    if (title) {
        const originalText = title.textContent;
        typeWriter(title, originalText, 150);
    }
});
</script>
{% endblock %}
