#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة التعرف على الوجوه
Face Recognition Module
"""

import cv2
import face_recognition
import numpy as np
import os
import pickle
import logging
from typing import List, Tuple, Dict, Optional
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class FaceRecognitionSystem:
    """نظام التعرف على الوجوه"""
    
    def __init__(self, known_faces_path: str = "known_faces", 
                 tolerance: float = 0.6, model: str = "hog"):
        self.known_faces_path = known_faces_path
        self.tolerance = tolerance
        self.model = model  # "hog" أو "cnn"
        
        # قوائم الوجوه المعروفة
        self.known_face_encodings = []
        self.known_face_names = []
        self.known_face_metadata = {}
        
        # إعدادات الكشف
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # إنشاء المجلد إذا لم يكن موجوداً
        os.makedirs(known_faces_path, exist_ok=True)
        
        # تحميل الوجوه المعروفة
        self.load_known_faces()
    
    def load_known_faces(self):
        """تحميل الوجوه المعروفة من المجلد"""
        try:
            encodings_file = os.path.join(self.known_faces_path, "encodings.pkl")
            metadata_file = os.path.join(self.known_faces_path, "metadata.json")
            
            if os.path.exists(encodings_file):
                with open(encodings_file, 'rb') as f:
                    data = pickle.load(f)
                    self.known_face_encodings = data.get('encodings', [])
                    self.known_face_names = data.get('names', [])
                
                logger.info(f"تم تحميل {len(self.known_face_encodings)} وجه معروف")
            
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    self.known_face_metadata = json.load(f)
            
        except Exception as e:
            logger.error(f"خطأ في تحميل الوجوه المعروفة: {str(e)}")
            self.known_face_encodings = []
            self.known_face_names = []
            self.known_face_metadata = {}
    
    def save_known_faces(self):
        """حفظ الوجوه المعروفة"""
        try:
            encodings_file = os.path.join(self.known_faces_path, "encodings.pkl")
            metadata_file = os.path.join(self.known_faces_path, "metadata.json")
            
            # حفظ التشفيرات
            data = {
                'encodings': self.known_face_encodings,
                'names': self.known_face_names
            }
            with open(encodings_file, 'wb') as f:
                pickle.dump(data, f)
            
            # حفظ البيانات الوصفية
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.known_face_metadata, f, ensure_ascii=False, indent=2)
            
            logger.info("تم حفظ الوجوه المعروفة بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الوجوه المعروفة: {str(e)}")
            return False
    
    def add_known_face(self, image_path: str, person_name: str, 
                       metadata: Dict = None) -> bool:
        """إضافة وجه جديد للقائمة المعروفة"""
        try:
            # قراءة الصورة
            image = face_recognition.load_image_file(image_path)
            
            # العثور على الوجوه في الصورة
            face_encodings = face_recognition.face_encodings(image, model=self.model)
            
            if len(face_encodings) == 0:
                logger.warning(f"لم يتم العثور على وجوه في الصورة: {image_path}")
                return False
            
            if len(face_encodings) > 1:
                logger.warning(f"تم العثور على أكثر من وجه في الصورة: {image_path}")
                # استخدام أول وجه
            
            # إضافة الوجه
            face_encoding = face_encodings[0]
            self.known_face_encodings.append(face_encoding)
            self.known_face_names.append(person_name)
            
            # إضافة البيانات الوصفية
            if metadata is None:
                metadata = {}
            
            metadata.update({
                'added_date': datetime.now().isoformat(),
                'image_path': image_path,
                'encoding_model': self.model
            })
            
            self.known_face_metadata[person_name] = metadata
            
            # حفظ التحديثات
            self.save_known_faces()
            
            logger.info(f"تم إضافة الوجه: {person_name}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إضافة الوجه {person_name}: {str(e)}")
            return False
    
    def remove_known_face(self, person_name: str) -> bool:
        """إزالة وجه من القائمة المعروفة"""
        try:
            if person_name in self.known_face_names:
                index = self.known_face_names.index(person_name)
                
                # إزالة من القوائم
                del self.known_face_encodings[index]
                del self.known_face_names[index]
                
                # إزالة البيانات الوصفية
                if person_name in self.known_face_metadata:
                    del self.known_face_metadata[person_name]
                
                # حفظ التحديثات
                self.save_known_faces()
                
                logger.info(f"تم حذف الوجه: {person_name}")
                return True
            else:
                logger.warning(f"الوجه غير موجود: {person_name}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في حذف الوجه {person_name}: {str(e)}")
            return False
    
    def recognize_faces(self, frame: np.ndarray) -> List[Dict]:
        """التعرف على الوجوه في الإطار"""
        try:
            # تحويل من BGR إلى RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # العثور على مواقع الوجوه
            face_locations = face_recognition.face_locations(rgb_frame, model=self.model)
            
            if len(face_locations) == 0:
                return []
            
            # الحصول على تشفيرات الوجوه
            face_encodings = face_recognition.face_encodings(rgb_frame, face_locations, model=self.model)
            
            results = []
            
            for i, face_encoding in enumerate(face_encodings):
                # مقارنة مع الوجوه المعروفة
                matches = face_recognition.compare_faces(
                    self.known_face_encodings, face_encoding, tolerance=self.tolerance
                )
                
                name = "غير معروف"
                confidence = 0.0
                
                if True in matches:
                    # حساب المسافات
                    face_distances = face_recognition.face_distance(
                        self.known_face_encodings, face_encoding
                    )
                    
                    best_match_index = np.argmin(face_distances)
                    
                    if matches[best_match_index]:
                        name = self.known_face_names[best_match_index]
                        # تحويل المسافة إلى نسبة ثقة
                        confidence = 1.0 - face_distances[best_match_index]
                
                # إحداثيات الوجه
                top, right, bottom, left = face_locations[i]
                
                result = {
                    'name': name,
                    'confidence': confidence,
                    'location': {
                        'top': top,
                        'right': right,
                        'bottom': bottom,
                        'left': left
                    },
                    'is_known': name != "غير معروف",
                    'metadata': self.known_face_metadata.get(name, {})
                }
                
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في التعرف على الوجوه: {str(e)}")
            return []
    
    def draw_face_boxes(self, frame: np.ndarray, face_results: List[Dict]) -> np.ndarray:
        """رسم مربعات حول الوجوه المكتشفة"""
        try:
            annotated_frame = frame.copy()
            
            for face in face_results:
                location = face['location']
                name = face['name']
                confidence = face['confidence']
                is_known = face['is_known']
                
                # تحديد لون المربع
                color = (0, 255, 0) if is_known else (0, 0, 255)  # أخضر للمعروف، أحمر للمجهول
                
                # رسم المربع
                cv2.rectangle(annotated_frame, 
                            (location['left'], location['top']), 
                            (location['right'], location['bottom']), 
                            color, 2)
                
                # إضافة النص
                label = f"{name}"
                if is_known:
                    label += f" ({confidence:.2f})"
                
                # خلفية للنص
                text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_DUPLEX, 0.6, 1)[0]
                cv2.rectangle(annotated_frame,
                            (location['left'], location['bottom'] - 35),
                            (location['left'] + text_size[0], location['bottom']),
                            color, cv2.FILLED)
                
                # النص
                cv2.putText(annotated_frame, label,
                          (location['left'] + 6, location['bottom'] - 6),
                          cv2.FONT_HERSHEY_DUPLEX, 0.6, (255, 255, 255), 1)
            
            return annotated_frame
            
        except Exception as e:
            logger.error(f"خطأ في رسم مربعات الوجوه: {str(e)}")
            return frame
    
    def detect_faces_opencv(self, frame: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """كشف الوجوه باستخدام OpenCV (أسرع ولكن أقل دقة)"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            faces = self.face_cascade.detectMultiScale(
                gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
            )
            
            # تحويل إلى تنسيق (top, right, bottom, left)
            face_locations = []
            for (x, y, w, h) in faces:
                face_locations.append((y, x + w, y + h, x))
            
            return face_locations
            
        except Exception as e:
            logger.error(f"خطأ في كشف الوجوه بـ OpenCV: {str(e)}")
            return []
    
    def get_statistics(self) -> Dict:
        """الحصول على إحصائيات النظام"""
        return {
            'total_known_faces': len(self.known_face_names),
            'known_faces': self.known_face_names.copy(),
            'model': self.model,
            'tolerance': self.tolerance,
            'metadata': self.known_face_metadata.copy()
        }
    
    def update_settings(self, tolerance: float = None, model: str = None):
        """تحديث إعدادات النظام"""
        if tolerance is not None:
            self.tolerance = tolerance
            logger.info(f"تم تحديث tolerance إلى: {tolerance}")
        
        if model is not None and model in ['hog', 'cnn']:
            self.model = model
            logger.info(f"تم تحديث model إلى: {model}")
            # إعادة تحميل الوجوه المعروفة بالنموذج الجديد
            self.load_known_faces()


# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء نظام التعرف على الوجوه
    face_system = FaceRecognitionSystem()
    
    # إضافة وجه جديد (مثال)
    # face_system.add_known_face("path/to/person.jpg", "اسم الشخص")
    
    # اختبار مع كاميرا
    cap = cv2.VideoCapture(0)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # التعرف على الوجوه
        faces = face_system.recognize_faces(frame)
        
        # رسم المربعات
        annotated_frame = face_system.draw_face_boxes(frame, faces)
        
        # عرض النتيجة
        cv2.imshow('Face Recognition', annotated_frame)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
