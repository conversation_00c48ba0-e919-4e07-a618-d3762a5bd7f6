@echo off
chcp 65001 >nul
title نظام المراقبة الذكي - Smart Surveillance System

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🎥 نظام المراقبة الذكي 🎥                          ║
echo ║              Smart Surveillance System                       ║
echo ║                                                              ║
echo ║              مشابه لجهاز Dahua DHI-XVR5116H                 ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص النظام...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متاح
    pause
    exit /b 1
)

echo ✅ pip متاح

REM التحقق من وجود ملف requirements.txt
if not exist requirements.txt (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo ✅ ملف requirements.txt موجود

REM التحقق من وجود ملف الإعدادات
if not exist config.json (
    echo ❌ ملف config.json غير موجود
    pause
    exit /b 1
)

echo ✅ ملف الإعدادات موجود

echo.
echo 📦 تثبيت المكتبات المطلوبة...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت جميع المكتبات بنجاح

echo.
echo 🚀 بدء تشغيل النظام...
echo.
echo 🌐 سيتم فتح النظام على: http://localhost:5000
echo 👤 بيانات الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo ⏹️  اضغط Ctrl+C لإيقاف النظام
echo.

REM تشغيل النظام
python run.py

echo.
echo ✅ تم إغلاق النظام
pause
