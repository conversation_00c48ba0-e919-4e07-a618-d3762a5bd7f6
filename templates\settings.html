{% extends "base.html" %}

{% block title %}الإعدادات - نظام المراقبة الذكي{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-cog me-2"></i>
                    إعدادات النظام
                </h2>
                <div class="btn-group" role="group">
                    <button class="btn btn-success" onclick="saveAllSettings()">
                        <i class="fas fa-save me-2"></i>
                        حفظ جميع الإعدادات
                    </button>
                    <button class="btn btn-warning" onclick="resetToDefaults()">
                        <i class="fas fa-undo me-2"></i>
                        استعادة الافتراضي
                    </button>
                    <button class="btn btn-info" onclick="exportSettings()">
                        <i class="fas fa-download me-2"></i>
                        تصدير الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        أقسام الإعدادات
                    </h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="#general" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات عامة
                    </a>
                    <a href="#server" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-server me-2"></i>
                        إعدادات الخادم
                    </a>
                    <a href="#cameras" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-video me-2"></i>
                        إعدادات الكاميرات
                    </a>
                    <a href="#recording" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-record-vinyl me-2"></i>
                        إعدادات التسجيل
                    </a>
                    <a href="#detection" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-eye me-2"></i>
                        كشف الحركة والذكاء الاصطناعي
                    </a>
                    <a href="#notifications" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-bell me-2"></i>
                        الإشعارات والتنبيهات
                    </a>
                    <a href="#storage" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-hdd me-2"></i>
                        إدارة التخزين
                    </a>
                    <a href="#security" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-shield-alt me-2"></i>
                        الأمان والخصوصية
                    </a>
                    <a href="#network" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-network-wired me-2"></i>
                        إعدادات الشبكة
                    </a>
                    <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                        <i class="fas fa-database me-2"></i>
                        النسخ الاحتياطي
                    </a>
                </div>
            </div>
        </div>

        <!-- محتوى الإعدادات -->
        <div class="col-md-9">
            <div class="tab-content">
                <!-- الإعدادات العامة -->
                <div class="tab-pane fade show active" id="general">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات العامة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="generalSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="systemName" class="form-label">اسم النظام</label>
                                            <input type="text" class="form-control" id="systemName"
                                                   value="نظام المراقبة الذكي">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="systemLanguage" class="form-label">لغة النظام</label>
                                            <select class="form-select" id="systemLanguage">
                                                <option value="ar" selected>العربية</option>
                                                <option value="en">English</option>
                                                <option value="fr">Français</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                            <select class="form-select" id="timezone">
                                                <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                                <option value="Asia/Dubai">دبي (GMT+4)</option>
                                                <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                                                <option value="UTC">UTC (GMT+0)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="dateFormat" class="form-label">تنسيق التاريخ</label>
                                            <select class="form-select" id="dateFormat">
                                                <option value="DD/MM/YYYY" selected>يوم/شهر/سنة</option>
                                                <option value="MM/DD/YYYY">شهر/يوم/سنة</option>
                                                <option value="YYYY-MM-DD">سنة-شهر-يوم</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="autoRefresh" class="form-label">تحديث تلقائي (ثانية)</label>
                                            <input type="number" class="form-control" id="autoRefresh"
                                                   value="30" min="5" max="300">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="maxCameras" class="form-label">الحد الأقصى للكاميرات</label>
                                            <input type="number" class="form-control" id="maxCameras"
                                                   value="16" min="1" max="64">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableDarkMode" checked>
                                        <label class="form-check-label" for="enableDarkMode">
                                            تفعيل الوضع المظلم
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableSounds" checked>
                                        <label class="form-check-label" for="enableSounds">
                                            تفعيل الأصوات والتنبيهات الصوتية
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الخادم -->
                <div class="tab-pane fade" id="server">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-server me-2"></i>
                                إعدادات الخادم
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="serverSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="serverHost" class="form-label">عنوان الخادم</label>
                                            <input type="text" class="form-control" id="serverHost"
                                                   value="0.0.0.0" placeholder="0.0.0.0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="serverPort" class="form-label">منفذ الخادم</label>
                                            <input type="number" class="form-control" id="serverPort"
                                                   value="5000" min="1" max="65535">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="maxConnections" class="form-label">الحد الأقصى للاتصالات</label>
                                            <input type="number" class="form-control" id="maxConnections"
                                                   value="100" min="1" max="1000">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sessionTimeout" class="form-label">انتهاء الجلسة (دقيقة)</label>
                                            <input type="number" class="form-control" id="sessionTimeout"
                                                   value="60" min="5" max="1440">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableSSL">
                                        <label class="form-check-label" for="enableSSL">
                                            تفعيل SSL/HTTPS
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableDebug">
                                        <label class="form-check-label" for="enableDebug">
                                            تفعيل وضع التطوير (Debug)
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="logLevel" class="form-label">مستوى السجلات</label>
                                    <select class="form-select" id="logLevel">
                                        <option value="DEBUG">تفصيلي (DEBUG)</option>
                                        <option value="INFO" selected>معلومات (INFO)</option>
                                        <option value="WARNING">تحذيرات (WARNING)</option>
                                        <option value="ERROR">أخطاء (ERROR)</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الكاميرات -->
                <div class="tab-pane fade" id="cameras">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-video me-2"></i>
                                إعدادات الكاميرات
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="cameraSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="defaultResolution" class="form-label">الدقة الافتراضية</label>
                                            <select class="form-select" id="defaultResolution">
                                                <option value="1920x1080" selected>1080p (1920×1080)</option>
                                                <option value="1280x720">720p (1280×720)</option>
                                                <option value="640x480">480p (640×480)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="defaultFPS" class="form-label">معدل الإطارات الافتراضي</label>
                                            <select class="form-select" id="defaultFPS">
                                                <option value="30" selected>30 FPS</option>
                                                <option value="25">25 FPS</option>
                                                <option value="15">15 FPS</option>
                                                <option value="10">10 FPS</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="streamQuality" class="form-label">جودة البث</label>
                                            <select class="form-select" id="streamQuality">
                                                <option value="high">عالية (90%)</option>
                                                <option value="medium" selected>متوسطة (70%)</option>
                                                <option value="low">منخفضة (50%)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="connectionTimeout" class="form-label">مهلة الاتصال (ثانية)</label>
                                            <input type="number" class="form-control" id="connectionTimeout"
                                                   value="10" min="5" max="60">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="maxRetries" class="form-label">عدد المحاولات</label>
                                            <input type="number" class="form-control" id="maxRetries"
                                                   value="3" min="1" max="10">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="bufferSize" class="form-label">حجم المخزن المؤقت</label>
                                            <select class="form-select" id="bufferSize">
                                                <option value="1" selected>1 إطار</option>
                                                <option value="2">2 إطار</option>
                                                <option value="3">3 إطار</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoReconnect" checked>
                                        <label class="form-check-label" for="autoReconnect">
                                            إعادة الاتصال التلقائي
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enhanceImage" checked>
                                        <label class="form-check-label" for="enhanceImage">
                                            تحسين جودة الصورة
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات التسجيل -->
                <div class="tab-pane fade" id="recording">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-record-vinyl me-2"></i>
                                إعدادات التسجيل
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="recordingSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="recordingPath" class="form-label">مجلد التسجيلات</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="recordingPath"
                                                       value="./recordings" readonly>
                                                <button class="btn btn-outline-secondary" type="button" onclick="selectFolder()">
                                                    <i class="fas fa-folder-open"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="recordingFormat" class="form-label">تنسيق التسجيل</label>
                                            <select class="form-select" id="recordingFormat">
                                                <option value="mp4" selected>MP4</option>
                                                <option value="avi">AVI</option>
                                                <option value="mkv">MKV</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="recordingQuality" class="form-label">جودة التسجيل</label>
                                            <select class="form-select" id="recordingQuality">
                                                <option value="high" selected>عالية</option>
                                                <option value="medium">متوسطة</option>
                                                <option value="low">منخفضة</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="segmentDuration" class="form-label">مدة المقطع (دقيقة)</label>
                                            <input type="number" class="form-control" id="segmentDuration"
                                                   value="10" min="1" max="60">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="maxStorageGB" class="form-label">الحد الأقصى للتخزين (GB)</label>
                                            <input type="number" class="form-control" id="maxStorageGB"
                                                   value="100" min="1" max="10000">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="retentionDays" class="form-label">مدة الاحتفاظ (يوم)</label>
                                            <input type="number" class="form-control" id="retentionDays"
                                                   value="30" min="1" max="365">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoRecording" checked>
                                        <label class="form-check-label" for="autoRecording">
                                            التسجيل التلقائي عند كشف الحركة
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="preRecording">
                                        <label class="form-check-label" for="preRecording">
                                            التسجيل المسبق (5 ثوان قبل الحدث)
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoCleanup" checked>
                                        <label class="form-check-label" for="autoCleanup">
                                            حذف التسجيلات القديمة تلقائياً
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- كشف الحركة والذكاء الاصطناعي -->
                <div class="tab-pane fade" id="detection">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-eye me-2"></i>
                                كشف الحركة والذكاء الاصطناعي
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="detectionSettingsForm">
                                <!-- كشف الحركة -->
                                <h6 class="mb-3">
                                    <i class="fas fa-running me-2"></i>
                                    كشف الحركة
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="motionSensitivity" class="form-label">حساسية كشف الحركة</label>
                                            <input type="range" class="form-range" id="motionSensitivity"
                                                   min="1" max="10" value="5">
                                            <div class="d-flex justify-content-between">
                                                <small>منخفضة</small>
                                                <small>متوسطة</small>
                                                <small>عالية</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="motionThreshold" class="form-label">عتبة كشف الحركة (%)</label>
                                            <input type="number" class="form-control" id="motionThreshold"
                                                   value="15" min="1" max="100">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableMotionDetection" checked>
                                        <label class="form-check-label" for="enableMotionDetection">
                                            تفعيل كشف الحركة
                                        </label>
                                    </div>
                                </div>

                                <hr>

                                <!-- كشف الوجوه -->
                                <h6 class="mb-3">
                                    <i class="fas fa-user-friends me-2"></i>
                                    كشف الوجوه
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="faceConfidence" class="form-label">دقة كشف الوجوه (%)</label>
                                            <input type="number" class="form-control" id="faceConfidence"
                                                   value="80" min="50" max="99">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="faceModel" class="form-label">نموذج كشف الوجوه</label>
                                            <select class="form-select" id="faceModel">
                                                <option value="hog" selected>HOG (سريع)</option>
                                                <option value="cnn">CNN (دقيق)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableFaceDetection" checked>
                                        <label class="form-check-label" for="enableFaceDetection">
                                            تفعيل كشف الوجوه
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableFaceRecognition">
                                        <label class="form-check-label" for="enableFaceRecognition">
                                            تفعيل التعرف على الوجوه
                                        </label>
                                    </div>
                                </div>

                                <hr>

                                <!-- كشف الأشياء -->
                                <h6 class="mb-3">
                                    <i class="fas fa-cube me-2"></i>
                                    كشف الأشياء (YOLO)
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="yoloModel" class="form-label">نموذج YOLO</label>
                                            <select class="form-select" id="yoloModel">
                                                <option value="yolov5s" selected>YOLOv5s (سريع)</option>
                                                <option value="yolov5m">YOLOv5m (متوسط)</option>
                                                <option value="yolov5l">YOLOv5l (دقيق)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="objectConfidence" class="form-label">دقة كشف الأشياء (%)</label>
                                            <input type="number" class="form-control" id="objectConfidence"
                                                   value="70" min="30" max="99">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="detectedObjects" class="form-label">الأشياء المراد كشفها</label>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="detectPerson" checked>
                                                <label class="form-check-label" for="detectPerson">أشخاص</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="detectCar" checked>
                                                <label class="form-check-label" for="detectCar">سيارات</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="detectAnimal">
                                                <label class="form-check-label" for="detectAnimal">حيوانات</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableObjectDetection">
                                        <label class="form-check-label" for="enableObjectDetection">
                                            تفعيل كشف الأشياء
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- الإشعارات والتنبيهات -->
                <div class="tab-pane fade" id="notifications">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bell me-2"></i>
                                الإشعارات والتنبيهات
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="notificationSettingsForm">
                                <!-- إشعارات البريد الإلكتروني -->
                                <h6 class="mb-3">
                                    <i class="fas fa-envelope me-2"></i>
                                    البريد الإلكتروني
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="smtpServer" class="form-label">خادم SMTP</label>
                                            <input type="text" class="form-control" id="smtpServer"
                                                   placeholder="smtp.gmail.com">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="smtpPort" class="form-label">منفذ SMTP</label>
                                            <input type="number" class="form-control" id="smtpPort"
                                                   value="587" min="1" max="65535">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emailUsername" class="form-label">اسم المستخدم</label>
                                            <input type="email" class="form-control" id="emailUsername"
                                                   placeholder="<EMAIL>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emailPassword" class="form-label">كلمة المرور</label>
                                            <input type="password" class="form-control" id="emailPassword">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notificationEmails" class="form-label">عناوين البريد للإشعارات</label>
                                    <textarea class="form-control" id="notificationEmails" rows="3"
                                              placeholder="<EMAIL>&#10;<EMAIL>"></textarea>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableEmailNotifications">
                                        <label class="form-check-label" for="enableEmailNotifications">
                                            تفعيل إشعارات البريد الإلكتروني
                                        </label>
                                    </div>
                                </div>

                                <hr>

                                <!-- إشعارات Telegram -->
                                <h6 class="mb-3">
                                    <i class="fab fa-telegram me-2"></i>
                                    Telegram
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="telegramBotToken" class="form-label">Bot Token</label>
                                            <input type="text" class="form-control" id="telegramBotToken"
                                                   placeholder="123456789:ABCdefGHIjklMNOpqrsTUVwxyz">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="telegramChatId" class="form-label">Chat ID</label>
                                            <input type="text" class="form-control" id="telegramChatId"
                                                   placeholder="-123456789">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableTelegramNotifications">
                                        <label class="form-check-label" for="enableTelegramNotifications">
                                            تفعيل إشعارات Telegram
                                        </label>
                                    </div>
                                </div>

                                <hr>

                                <!-- أنواع الإشعارات -->
                                <h6 class="mb-3">
                                    <i class="fas fa-list me-2"></i>
                                    أنواع الإشعارات
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notifyMotion" checked>
                                            <label class="form-check-label" for="notifyMotion">
                                                كشف الحركة
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notifyFace" checked>
                                            <label class="form-check-label" for="notifyFace">
                                                كشف الوجوه
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notifyObject">
                                            <label class="form-check-label" for="notifyObject">
                                                كشف الأشياء
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notifySystem" checked>
                                            <label class="form-check-label" for="notifySystem">
                                                أحداث النظام
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notifyConnection">
                                            <label class="form-check-label" for="notifyConnection">
                                                انقطاع الاتصال
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notifyStorage">
                                            <label class="form-check-label" for="notifyStorage">
                                                امتلاء التخزين
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إدارة التخزين -->
                <div class="tab-pane fade" id="storage">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-hdd me-2"></i>
                                إدارة التخزين
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="storageSettingsForm">
                                <!-- معلومات التخزين -->
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <div class="card bg-primary">
                                            <div class="card-body text-center">
                                                <h5 class="card-title">المساحة المستخدمة</h5>
                                                <h3 id="usedStorage">45.2 GB</h3>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-success">
                                            <div class="card-body text-center">
                                                <h5 class="card-title">المساحة المتاحة</h5>
                                                <h3 id="availableStorage">154.8 GB</h3>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-info">
                                            <div class="card-body text-center">
                                                <h5 class="card-title">إجمالي المساحة</h5>
                                                <h3 id="totalStorage">200 GB</h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- شريط التقدم -->
                                <div class="mb-4">
                                    <label class="form-label">استخدام التخزين</label>
                                    <div class="progress" style="height: 25px;">
                                        <div class="progress-bar" style="width: 22.6%">22.6%</div>
                                    </div>
                                </div>

                                <!-- إعدادات التنظيف -->
                                <h6 class="mb-3">
                                    <i class="fas fa-broom me-2"></i>
                                    إعدادات التنظيف التلقائي
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="cleanupThreshold" class="form-label">عتبة التنظيف (%)</label>
                                            <input type="number" class="form-control" id="cleanupThreshold"
                                                   value="80" min="50" max="95">
                                            <div class="form-text">بدء التنظيف عند امتلاء التخزين بهذه النسبة</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="cleanupStrategy" class="form-label">استراتيجية التنظيف</label>
                                            <select class="form-select" id="cleanupStrategy">
                                                <option value="oldest" selected>حذف الأقدم أولاً</option>
                                                <option value="largest">حذف الأكبر أولاً</option>
                                                <option value="quality">حذف الأقل جودة أولاً</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableAutoCleanup" checked>
                                        <label class="form-check-label" for="enableAutoCleanup">
                                            تفعيل التنظيف التلقائي
                                        </label>
                                    </div>
                                </div>

                                <!-- أدوات التنظيف -->
                                <h6 class="mb-3">
                                    <i class="fas fa-tools me-2"></i>
                                    أدوات التنظيف
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-warning w-100 mb-2" onclick="cleanOldRecordings()">
                                            <i class="fas fa-trash me-2"></i>
                                            حذف التسجيلات القديمة
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-info w-100 mb-2" onclick="cleanTempFiles()">
                                            <i class="fas fa-file-alt me-2"></i>
                                            حذف الملفات المؤقتة
                                        </button>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-secondary w-100 mb-2" onclick="cleanLogs()">
                                            <i class="fas fa-list me-2"></i>
                                            حذف السجلات القديمة
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-danger w-100 mb-2" onclick="cleanAll()">
                                            <i class="fas fa-broom me-2"></i>
                                            تنظيف شامل
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- الأمان والخصوصية -->
                <div class="tab-pane fade" id="security">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-shield-alt me-2"></i>
                                الأمان والخصوصية
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="securitySettingsForm">
                                <!-- إعدادات كلمة المرور -->
                                <h6 class="mb-3">
                                    <i class="fas fa-key me-2"></i>
                                    سياسة كلمات المرور
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="minPasswordLength" class="form-label">الحد الأدنى لطول كلمة المرور</label>
                                            <input type="number" class="form-control" id="minPasswordLength"
                                                   value="8" min="6" max="32">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="passwordExpiry" class="form-label">انتهاء صلاحية كلمة المرور (يوم)</label>
                                            <input type="number" class="form-control" id="passwordExpiry"
                                                   value="90" min="30" max="365">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="requireComplexPassword" checked>
                                        <label class="form-check-label" for="requireComplexPassword">
                                            طلب كلمة مرور معقدة (أحرف كبيرة وصغيرة وأرقام ورموز)
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableTwoFactor">
                                        <label class="form-check-label" for="enableTwoFactor">
                                            تفعيل المصادقة الثنائية (2FA)
                                        </label>
                                    </div>
                                </div>

                                <hr>

                                <!-- إعدادات الجلسات -->
                                <h6 class="mb-3">
                                    <i class="fas fa-clock me-2"></i>
                                    إعدادات الجلسات
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="maxLoginAttempts" class="form-label">الحد الأقصى لمحاولات الدخول</label>
                                            <input type="number" class="form-control" id="maxLoginAttempts"
                                                   value="5" min="3" max="10">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="lockoutDuration" class="form-label">مدة الحظر (دقيقة)</label>
                                            <input type="number" class="form-control" id="lockoutDuration"
                                                   value="15" min="5" max="60">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableSessionLogging" checked>
                                        <label class="form-check-label" for="enableSessionLogging">
                                            تسجيل جلسات المستخدمين
                                        </label>
                                    </div>
                                </div>

                                <hr>

                                <!-- إعدادات الشبكة -->
                                <h6 class="mb-3">
                                    <i class="fas fa-network-wired me-2"></i>
                                    أمان الشبكة
                                </h6>

                                <div class="mb-3">
                                    <label for="allowedIPs" class="form-label">عناوين IP المسموحة</label>
                                    <textarea class="form-control" id="allowedIPs" rows="3"
                                              placeholder="***********/24&#10;10.0.0.0/8&#10;**********/12"></textarea>
                                    <div class="form-text">اتركه فارغاً للسماح لجميع العناوين</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableIPWhitelist">
                                        <label class="form-check-label" for="enableIPWhitelist">
                                            تفعيل قائمة IP البيضاء
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableRateLimit" checked>
                                        <label class="form-check-label" for="enableRateLimit">
                                            تفعيل حد معدل الطلبات
                                        </label>
                                    </div>
                                </div>

                                <hr>

                                <!-- إعدادات التشفير -->
                                <h6 class="mb-3">
                                    <i class="fas fa-lock me-2"></i>
                                    التشفير
                                </h6>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="encryptRecordings">
                                        <label class="form-check-label" for="encryptRecordings">
                                            تشفير ملفات التسجيل
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="encryptDatabase">
                                        <label class="form-check-label" for="encryptDatabase">
                                            تشفير قاعدة البيانات
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الشبكة -->
                <div class="tab-pane fade" id="network">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-network-wired me-2"></i>
                                إعدادات الشبكة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="networkSettingsForm">
                                <!-- إعدادات الاكتشاف -->
                                <h6 class="mb-3">
                                    <i class="fas fa-search me-2"></i>
                                    الاكتشاف التلقائي
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="defaultNetworkRange" class="form-label">نطاق الشبكة الافتراضي</label>
                                            <input type="text" class="form-control" id="defaultNetworkRange"
                                                   value="192.168.1.1-254">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="scanTimeout" class="form-label">مهلة الفحص (ثانية)</label>
                                            <input type="number" class="form-control" id="scanTimeout"
                                                   value="5" min="1" max="30">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="defaultPorts" class="form-label">المنافذ الافتراضية للفحص</label>
                                    <input type="text" class="form-control" id="defaultPorts"
                                           value="554,8080,80,8000,8554">
                                </div>

                                <hr>

                                <!-- إعدادات البروتوكولات -->
                                <h6 class="mb-3">
                                    <i class="fas fa-cogs me-2"></i>
                                    البروتوكولات المدعومة
                                </h6>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableRTSP" checked>
                                            <label class="form-check-label" for="enableRTSP">RTSP</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableHTTP" checked>
                                            <label class="form-check-label" for="enableHTTP">HTTP/MJPEG</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableONVIF" checked>
                                            <label class="form-check-label" for="enableONVIF">ONVIF</label>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <!-- إعدادات الاتصال -->
                                <h6 class="mb-3">
                                    <i class="fas fa-wifi me-2"></i>
                                    إعدادات الاتصال
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="connectionPoolSize" class="form-label">حجم مجموعة الاتصالات</label>
                                            <input type="number" class="form-control" id="connectionPoolSize"
                                                   value="10" min="5" max="50">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="keepAliveInterval" class="form-label">فترة Keep-Alive (ثانية)</label>
                                            <input type="number" class="form-control" id="keepAliveInterval"
                                                   value="30" min="10" max="300">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableNetworkMonitoring" checked>
                                        <label class="form-check-label" for="enableNetworkMonitoring">
                                            تفعيل مراقبة الشبكة
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- النسخ الاحتياطي -->
                <div class="tab-pane fade" id="backup">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-database me-2"></i>
                                النسخ الاحتياطي والاستعادة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="backupSettingsForm">
                                <!-- النسخ الاحتياطي التلقائي -->
                                <h6 class="mb-3">
                                    <i class="fas fa-clock me-2"></i>
                                    النسخ الاحتياطي التلقائي
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="backupFrequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                            <select class="form-select" id="backupFrequency">
                                                <option value="daily" selected>يومي</option>
                                                <option value="weekly">أسبوعي</option>
                                                <option value="monthly">شهري</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="backupTime" class="form-label">وقت النسخ الاحتياطي</label>
                                            <input type="time" class="form-control" id="backupTime" value="02:00">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="backupPath" class="form-label">مجلد النسخ الاحتياطي</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="backupPath"
                                               value="./backups" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="selectBackupFolder()">
                                            <i class="fas fa-folder-open"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="maxBackups" class="form-label">الحد الأقصى للنسخ المحفوظة</label>
                                            <input type="number" class="form-control" id="maxBackups"
                                                   value="7" min="1" max="30">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="compressionLevel" class="form-label">مستوى الضغط</label>
                                            <select class="form-select" id="compressionLevel">
                                                <option value="none">بدون ضغط</option>
                                                <option value="low">ضغط منخفض</option>
                                                <option value="medium" selected>ضغط متوسط</option>
                                                <option value="high">ضغط عالي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableAutoBackup" checked>
                                        <label class="form-check-label" for="enableAutoBackup">
                                            تفعيل النسخ الاحتياطي التلقائي
                                        </label>
                                    </div>
                                </div>

                                <hr>

                                <!-- أدوات النسخ الاحتياطي -->
                                <h6 class="mb-3">
                                    <i class="fas fa-tools me-2"></i>
                                    أدوات النسخ الاحتياطي
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-primary w-100 mb-2" onclick="createBackup()">
                                            <i class="fas fa-download me-2"></i>
                                            إنشاء نسخة احتياطية الآن
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-success w-100 mb-2" onclick="restoreBackup()">
                                            <i class="fas fa-upload me-2"></i>
                                            استعادة من نسخة احتياطية
                                        </button>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-info w-100 mb-2" onclick="exportSettings()">
                                            <i class="fas fa-file-export me-2"></i>
                                            تصدير الإعدادات
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-warning w-100 mb-2" onclick="importSettings()">
                                            <i class="fas fa-file-import me-2"></i>
                                            استيراد الإعدادات
                                        </button>
                                    </div>
                                </div>

                                <hr>

                                <!-- قائمة النسخ الاحتياطية -->
                                <h6 class="mb-3">
                                    <i class="fas fa-list me-2"></i>
                                    النسخ الاحتياطية المتاحة
                                </h6>

                                <div class="table-responsive">
                                    <table class="table table-dark table-hover">
                                        <thead>
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>الحجم</th>
                                                <th>النوع</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="backupsList">
                                            <tr>
                                                <td>2024-01-15 02:00:00</td>
                                                <td>45.2 MB</td>
                                                <td><span class="badge bg-success">تلقائي</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-success me-1" onclick="restoreSpecificBackup('backup_20240115.zip')">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup('backup_20240115.zip')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let currentSettings = {};

// تحميل الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadAllSettings();
    updateStorageInfo();
    loadBackupsList();

    // إضافة مستمعات للتبديل بين الأقسام
    document.querySelectorAll('[data-bs-toggle="pill"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const targetId = e.target.getAttribute('href').substring(1);
            loadSectionSettings(targetId);
        });
    });
});

// تحميل جميع الإعدادات
function loadAllSettings() {
    fetch('/api/settings')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentSettings = data.settings;
                populateSettingsForm();
            } else {
                showAlert('فشل في تحميل الإعدادات', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الإعدادات:', error);
            showAlert('خطأ في الاتصال بالخادم', 'error');
        });
}

// ملء النماذج بالإعدادات
function populateSettingsForm() {
    // الإعدادات العامة
    if (currentSettings.general) {
        document.getElementById('systemName').value = currentSettings.general.system_name || 'نظام المراقبة الذكي';
        document.getElementById('systemLanguage').value = currentSettings.general.language || 'ar';
        document.getElementById('timezone').value = currentSettings.general.timezone || 'Asia/Riyadh';
        document.getElementById('dateFormat').value = currentSettings.general.date_format || 'DD/MM/YYYY';
        document.getElementById('autoRefresh').value = currentSettings.general.auto_refresh || 30;
        document.getElementById('maxCameras').value = currentSettings.general.max_cameras || 16;
        document.getElementById('enableDarkMode').checked = currentSettings.general.dark_mode !== false;
        document.getElementById('enableSounds').checked = currentSettings.general.enable_sounds !== false;
    }

    // إعدادات الخادم
    if (currentSettings.server) {
        document.getElementById('serverHost').value = currentSettings.server.host || '0.0.0.0';
        document.getElementById('serverPort').value = currentSettings.server.port || 5000;
        document.getElementById('maxConnections').value = currentSettings.server.max_connections || 100;
        document.getElementById('sessionTimeout').value = currentSettings.server.session_timeout || 60;
        document.getElementById('enableSSL').checked = currentSettings.server.enable_ssl || false;
        document.getElementById('enableDebug').checked = currentSettings.server.debug || false;
        document.getElementById('logLevel').value = currentSettings.server.log_level || 'INFO';
    }

    // إعدادات الكاميرات
    if (currentSettings.cameras) {
        document.getElementById('defaultResolution').value = currentSettings.cameras.default_resolution || '1920x1080';
        document.getElementById('defaultFPS').value = currentSettings.cameras.default_fps || 30;
        document.getElementById('streamQuality').value = currentSettings.cameras.stream_quality || 'medium';
        document.getElementById('connectionTimeout').value = currentSettings.cameras.connection_timeout || 10;
        document.getElementById('maxRetries').value = currentSettings.cameras.max_retries || 3;
        document.getElementById('bufferSize').value = currentSettings.cameras.buffer_size || 1;
        document.getElementById('autoReconnect').checked = currentSettings.cameras.auto_reconnect !== false;
        document.getElementById('enhanceImage').checked = currentSettings.cameras.enhance_image !== false;
    }

    // باقي الإعدادات...
    populateOtherSettings();
}

// ملء باقي الإعدادات
function populateOtherSettings() {
    // إعدادات التسجيل
    if (currentSettings.recording) {
        document.getElementById('recordingPath').value = currentSettings.recording.path || './recordings';
        document.getElementById('recordingFormat').value = currentSettings.recording.format || 'mp4';
        document.getElementById('recordingQuality').value = currentSettings.recording.quality || 'high';
        document.getElementById('segmentDuration').value = currentSettings.recording.segment_duration || 10;
        document.getElementById('maxStorageGB').value = currentSettings.recording.max_storage_gb || 100;
        document.getElementById('retentionDays').value = currentSettings.recording.retention_days || 30;
        document.getElementById('autoRecording').checked = currentSettings.recording.auto_recording !== false;
        document.getElementById('preRecording').checked = currentSettings.recording.pre_recording || false;
        document.getElementById('autoCleanup').checked = currentSettings.recording.auto_cleanup !== false;
    }

    // إعدادات كشف الحركة
    if (currentSettings.detection) {
        document.getElementById('motionSensitivity').value = currentSettings.detection.motion_sensitivity || 5;
        document.getElementById('motionThreshold').value = currentSettings.detection.motion_threshold || 15;
        document.getElementById('enableMotionDetection').checked = currentSettings.detection.enable_motion !== false;
        document.getElementById('faceConfidence').value = currentSettings.detection.face_confidence || 80;
        document.getElementById('faceModel').value = currentSettings.detection.face_model || 'hog';
        document.getElementById('enableFaceDetection').checked = currentSettings.detection.enable_face !== false;
        document.getElementById('enableFaceRecognition').checked = currentSettings.detection.enable_face_recognition || false;
        document.getElementById('yoloModel').value = currentSettings.detection.yolo_model || 'yolov5s';
        document.getElementById('objectConfidence').value = currentSettings.detection.object_confidence || 70;
        document.getElementById('detectPerson').checked = currentSettings.detection.detect_person !== false;
        document.getElementById('detectCar').checked = currentSettings.detection.detect_car !== false;
        document.getElementById('detectAnimal').checked = currentSettings.detection.detect_animal || false;
        document.getElementById('enableObjectDetection').checked = currentSettings.detection.enable_object || false;
    }
}

// حفظ جميع الإعدادات
function saveAllSettings() {
    const settings = collectAllSettings();

    fetch('/api/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حفظ الإعدادات بنجاح', 'success');
            currentSettings = settings;
        } else {
            showAlert('فشل في حفظ الإعدادات: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('خطأ في حفظ الإعدادات:', error);
        showAlert('خطأ في الاتصال بالخادم', 'error');
    });
}

// جمع جميع الإعدادات من النماذج
function collectAllSettings() {
    return {
        general: {
            system_name: document.getElementById('systemName').value,
            language: document.getElementById('systemLanguage').value,
            timezone: document.getElementById('timezone').value,
            date_format: document.getElementById('dateFormat').value,
            auto_refresh: parseInt(document.getElementById('autoRefresh').value),
            max_cameras: parseInt(document.getElementById('maxCameras').value),
            dark_mode: document.getElementById('enableDarkMode').checked,
            enable_sounds: document.getElementById('enableSounds').checked
        },
        server: {
            host: document.getElementById('serverHost').value,
            port: parseInt(document.getElementById('serverPort').value),
            max_connections: parseInt(document.getElementById('maxConnections').value),
            session_timeout: parseInt(document.getElementById('sessionTimeout').value),
            enable_ssl: document.getElementById('enableSSL').checked,
            debug: document.getElementById('enableDebug').checked,
            log_level: document.getElementById('logLevel').value
        },
        cameras: {
            default_resolution: document.getElementById('defaultResolution').value,
            default_fps: parseInt(document.getElementById('defaultFPS').value),
            stream_quality: document.getElementById('streamQuality').value,
            connection_timeout: parseInt(document.getElementById('connectionTimeout').value),
            max_retries: parseInt(document.getElementById('maxRetries').value),
            buffer_size: parseInt(document.getElementById('bufferSize').value),
            auto_reconnect: document.getElementById('autoReconnect').checked,
            enhance_image: document.getElementById('enhanceImage').checked
        },
        recording: {
            path: document.getElementById('recordingPath').value,
            format: document.getElementById('recordingFormat').value,
            quality: document.getElementById('recordingQuality').value,
            segment_duration: parseInt(document.getElementById('segmentDuration').value),
            max_storage_gb: parseInt(document.getElementById('maxStorageGB').value),
            retention_days: parseInt(document.getElementById('retentionDays').value),
            auto_recording: document.getElementById('autoRecording').checked,
            pre_recording: document.getElementById('preRecording').checked,
            auto_cleanup: document.getElementById('autoCleanup').checked
        },
        detection: {
            motion_sensitivity: parseInt(document.getElementById('motionSensitivity').value),
            motion_threshold: parseInt(document.getElementById('motionThreshold').value),
            enable_motion: document.getElementById('enableMotionDetection').checked,
            face_confidence: parseInt(document.getElementById('faceConfidence').value),
            face_model: document.getElementById('faceModel').value,
            enable_face: document.getElementById('enableFaceDetection').checked,
            enable_face_recognition: document.getElementById('enableFaceRecognition').checked,
            yolo_model: document.getElementById('yoloModel').value,
            object_confidence: parseInt(document.getElementById('objectConfidence').value),
            detect_person: document.getElementById('detectPerson').checked,
            detect_car: document.getElementById('detectCar').checked,
            detect_animal: document.getElementById('detectAnimal').checked,
            enable_object: document.getElementById('enableObjectDetection').checked
        }
    };
}

// استعادة الإعدادات الافتراضية
function resetToDefaults() {
    if (confirm('هل أنت متأكد من استعادة الإعدادات الافتراضية؟ سيتم فقدان جميع الإعدادات الحالية.')) {
        fetch('/api/settings/reset', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم استعادة الإعدادات الافتراضية', 'success');
                loadAllSettings();
            } else {
                showAlert('فشل في استعادة الإعدادات الافتراضية', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في استعادة الإعدادات:', error);
            showAlert('خطأ في الاتصال بالخادم', 'error');
        });
    }
}

// تصدير الإعدادات
function exportSettings() {
    fetch('/api/settings/export')
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `surveillance_settings_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            showAlert('تم تصدير الإعدادات بنجاح', 'success');
        })
        .catch(error => {
            console.error('خطأ في تصدير الإعدادات:', error);
            showAlert('فشل في تصدير الإعدادات', 'error');
        });
}

// استيراد الإعدادات
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const formData = new FormData();
            formData.append('settings_file', file);

            fetch('/api/settings/import', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('تم استيراد الإعدادات بنجاح', 'success');
                    loadAllSettings();
                } else {
                    showAlert('فشل في استيراد الإعدادات: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('خطأ في استيراد الإعدادات:', error);
                showAlert('فشل في استيراد الإعدادات', 'error');
            });
        }
    };
    input.click();
}

// وظائف مساعدة أخرى
function selectFolder() {
    showAlert('يرجى استخدام مدير الملفات لتحديد المجلد', 'info');
}

function selectBackupFolder() {
    showAlert('يرجى استخدام مدير الملفات لتحديد مجلد النسخ الاحتياطي', 'info');
}

function createBackup() {
    showAlert('جاري إنشاء نسخة احتياطية...', 'info');

    fetch('/api/backup/create', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            loadBackupsList();
        } else {
            showAlert('فشل في إنشاء النسخة الاحتياطية', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        showAlert('خطأ في إنشاء النسخة الاحتياطية', 'error');
    });
}

function restoreBackup() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.zip';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                const formData = new FormData();
                formData.append('backup_file', file);

                fetch('/api/backup/restore', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showAlert('فشل في استعادة النسخة الاحتياطية', 'error');
                    }
                })
                .catch(error => {
                    console.error('خطأ في استعادة النسخة الاحتياطية:', error);
                    showAlert('خطأ في استعادة النسخة الاحتياطية', 'error');
                });
            }
        }
    };
    input.click();
}

function loadBackupsList() {
    fetch('/api/backup/list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateBackupsList(data.backups);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل قائمة النسخ الاحتياطية:', error);
        });
}

function updateBackupsList(backups) {
    const tbody = document.getElementById('backupsList');
    tbody.innerHTML = '';

    backups.forEach(backup => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${backup.date}</td>
            <td>${backup.size}</td>
            <td><span class="badge bg-${backup.type === 'auto' ? 'success' : 'primary'}">${backup.type === 'auto' ? 'تلقائي' : 'يدوي'}</span></td>
            <td>
                <button class="btn btn-sm btn-outline-success me-1" onclick="restoreSpecificBackup('${backup.filename}')">
                    <i class="fas fa-undo"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup('${backup.filename}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function restoreSpecificBackup(filename) {
    if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟')) {
        fetch(`/api/backup/restore/${filename}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                showAlert('فشل في استعادة النسخة الاحتياطية', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            showAlert('خطأ في استعادة النسخة الاحتياطية', 'error');
        });
    }
}

function deleteBackup(filename) {
    if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
        fetch(`/api/backup/delete/${filename}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف النسخة الاحتياطية', 'success');
                loadBackupsList();
            } else {
                showAlert('فشل في حذف النسخة الاحتياطية', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في حذف النسخة الاحتياطية:', error);
            showAlert('خطأ في حذف النسخة الاحتياطية', 'error');
        });
    }
}

// وظائف التنظيف
function cleanOldRecordings() {
    if (confirm('هل أنت متأكد من حذف التسجيلات القديمة؟')) {
        fetch('/api/storage/clean/recordings', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(`تم حذف ${data.deleted_count} ملف تسجيل`, 'success');
                updateStorageInfo();
            } else {
                showAlert('فشل في حذف التسجيلات القديمة', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في حذف التسجيلات:', error);
            showAlert('خطأ في حذف التسجيلات', 'error');
        });
    }
}

function cleanTempFiles() {
    fetch('/api/storage/clean/temp', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`تم حذف ${data.deleted_count} ملف مؤقت`, 'success');
            updateStorageInfo();
        } else {
            showAlert('فشل في حذف الملفات المؤقتة', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ في حذف الملفات المؤقتة:', error);
        showAlert('خطأ في حذف الملفات المؤقتة', 'error');
    });
}

function cleanLogs() {
    if (confirm('هل أنت متأكد من حذف السجلات القديمة؟')) {
        fetch('/api/storage/clean/logs', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(`تم حذف ${data.deleted_count} ملف سجل`, 'success');
                updateStorageInfo();
            } else {
                showAlert('فشل في حذف السجلات القديمة', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في حذف السجلات:', error);
            showAlert('خطأ في حذف السجلات', 'error');
        });
    }
}

function cleanAll() {
    if (confirm('هل أنت متأكد من التنظيف الشامل؟ سيتم حذف جميع الملفات القديمة والمؤقتة.')) {
        fetch('/api/storage/clean/all', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(`تم التنظيف الشامل بنجاح. تم حذف ${data.deleted_count} ملف`, 'success');
                updateStorageInfo();
            } else {
                showAlert('فشل في التنظيف الشامل', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في التنظيف الشامل:', error);
            showAlert('خطأ في التنظيف الشامل', 'error');
        });
    }
}

function updateStorageInfo() {
    fetch('/api/storage/info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('usedStorage').textContent = data.used;
                document.getElementById('availableStorage').textContent = data.available;
                document.getElementById('totalStorage').textContent = data.total;

                // تحديث شريط التقدم
                const progressBar = document.querySelector('.progress-bar');
                const percentage = (parseFloat(data.used) / parseFloat(data.total) * 100).toFixed(1);
                progressBar.style.width = percentage + '%';
                progressBar.textContent = percentage + '%';
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث معلومات التخزين:', error);
        });
}

function loadSectionSettings(sectionId) {
    console.log('تحميل إعدادات القسم:', sectionId);
}

// عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}