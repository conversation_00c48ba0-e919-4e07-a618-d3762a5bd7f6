#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التشغيل السريع لنظام المراقبة الذكي
Quick Start Script for Smart Surveillance System
"""

import os
import sys
import subprocess
import json
import logging
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_packages = [
        'flask', 'opencv-python', 'flask-socketio', 
        'flask-login', 'flask-sqlalchemy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} غير مثبت")
    
    return missing_packages

def install_dependencies():
    """تثبيت المكتبات المطلوبة"""
    print("\n🔄 جاري تثبيت المكتبات المطلوبة...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ تم تثبيت جميع المكتبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'logs', 'recordings', 'known_faces', 'database',
        'static/css', 'static/js', 'static/images'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"📁 تم إنشاء المجلد: {directory}")

def check_config():
    """التحقق من ملف الإعدادات"""
    if not os.path.exists('config.json'):
        print("❌ ملف الإعدادات غير موجود")
        return False
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ ملف الإعدادات صحيح")
        return True
    except json.JSONDecodeError:
        print("❌ خطأ في ملف الإعدادات")
        return False

def setup_logging():
    """إعداد نظام السجلات"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/startup.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║           🎥 نظام المراقبة الذكي 🎥                          ║
    ║              Smart Surveillance System                       ║
    ║                                                              ║
    ║              مشابه لجهاز Dahua DHI-XVR5116H                 ║
    ║                                                              ║
    ║  الميزات:                                                    ║
    ║  • دعم 16 كاميرا متزامنة                                    ║
    ║  • بث مباشر عالي الجودة                                     ║
    ║  • ذكاء اصطناعي (تعرف على الوجوه)                         ║
    ║  • كشف الحركة الذكي                                         ║
    ║  • تسجيل تلقائي                                             ║
    ║  • واجهة عربية متكاملة                                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_system_info():
    """طباعة معلومات النظام"""
    print("\n📊 معلومات النظام:")
    print(f"🖥️  نظام التشغيل: {os.name}")
    print(f"🐍 Python: {sys.version}")
    print(f"📁 مجلد العمل: {os.getcwd()}")
    
    # معلومات الذاكرة (إذا كانت متاحة)
    try:
        import psutil
        memory = psutil.virtual_memory()
        print(f"💾 الذاكرة المتاحة: {memory.available // (1024**3)} GB")
    except ImportError:
        pass

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    print_system_info()
    
    print("\n🔍 فحص النظام...")
    
    # التحقق من إصدار Python
    if not check_python_version():
        return False
    
    # إنشاء المجلدات
    create_directories()
    
    # إعداد السجلات
    setup_logging()
    
    # التحقق من ملف الإعدادات
    if not check_config():
        print("⚠️  تأكد من وجود ملف config.json")
        return False
    
    # التحقق من المكتبات
    missing = check_dependencies()
    
    if missing:
        print(f"\n⚠️  المكتبات المفقودة: {', '.join(missing)}")
        response = input("هل تريد تثبيتها الآن؟ (y/n): ")
        
        if response.lower() in ['y', 'yes', 'نعم']:
            if not install_dependencies():
                return False
        else:
            print("❌ لا يمكن تشغيل النظام بدون المكتبات المطلوبة")
            return False
    
    print("\n✅ جميع الفحوصات مكتملة!")
    print("\n🚀 بدء تشغيل النظام...")
    
    # تشغيل التطبيق
    try:
        from app import app, socketio, config
        
        host = config.get('server', {}).get('host', '0.0.0.0')
        port = config.get('server', {}).get('port', 5000)
        debug = config.get('system', {}).get('debug', True)
        
        print(f"\n🌐 النظام يعمل على: http://{host}:{port}")
        print("👤 بيانات الدخول الافتراضية:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("\n⏹️  اضغط Ctrl+C لإيقاف النظام")
        
        socketio.run(app, host=host, port=port, debug=debug, allow_unsafe_werkzeug=True)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ فشل في تشغيل النظام")
        input("اضغط Enter للخروج...")
        sys.exit(1)
    else:
        print("\n✅ تم إغلاق النظام بنجاح")
