2025-06-12 20:25:13,999 - __main__ - INFO - تم إنشاء المستخدم الافتراضي: admin/admin123
2025-06-12 20:25:14,000 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:25:14,012 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-12 20:25:14,012 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-12 20:25:14,012 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:25:15,070 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:25:15,089 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:25:15,091 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:25:15,127 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:15] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-12 20:25:15,148 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:15] "GET /login?next=/ HTTP/1.1" 200 -
2025-06-12 20:25:32,157 - __main__ - INFO - تسجيل دخول ناجح للمستخدم: admin
2025-06-12 20:25:32,157 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:32] "[32mPOST /login?next=/ HTTP/1.1[0m" 302 -
2025-06-12 20:25:32,188 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:32] "GET / HTTP/1.1" 200 -
2025-06-12 20:25:32,529 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:32] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:25:32,530 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:32] "GET /socket.io/?EIO=4&transport=polling&t=PTbBEku HTTP/1.1" 200 -
2025-06-12 20:25:32,786 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:32] "POST /socket.io/?EIO=4&transport=polling&t=PTbBEpu&sid=Tiw0gBLv3YtV2E_EAAAA HTTP/1.1" 200 -
2025-06-12 20:25:32,846 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:32] "GET /socket.io/?EIO=4&transport=polling&t=PTbBEpv&sid=Tiw0gBLv3YtV2E_EAAAA HTTP/1.1" 200 -
2025-06-12 20:25:32,855 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:32] "GET /socket.io/?EIO=4&transport=polling&t=PTbBEuq&sid=Tiw0gBLv3YtV2E_EAAAA HTTP/1.1" 200 -
2025-06-12 20:25:33,168 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:33] "POST /socket.io/?EIO=4&transport=polling&t=PTbBEuq.0&sid=Tiw0gBLv3YtV2E_EAAAA HTTP/1.1" 200 -
2025-06-12 20:25:35,764 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:35] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:25:35,806 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:35] "GET /socket.io/?EIO=4&transport=websocket&sid=Tiw0gBLv3YtV2E_EAAAA HTTP/1.1" 200 -
2025-06-12 20:25:36,025 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:36] "GET /socket.io/?EIO=4&transport=polling&t=PTbBFck HTTP/1.1" 200 -
2025-06-12 20:25:36,108 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:36] "POST /socket.io/?EIO=4&transport=polling&t=PTbBFgV&sid=UeQ1JAi0S_yRFlsnAAAC HTTP/1.1" 200 -
2025-06-12 20:25:36,340 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:36] "GET /socket.io/?EIO=4&transport=polling&t=PTbBFgW&sid=UeQ1JAi0S_yRFlsnAAAC HTTP/1.1" 200 -
2025-06-12 20:25:36,353 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:36] "POST /socket.io/?EIO=4&transport=polling&t=PTbBFlQ&sid=UeQ1JAi0S_yRFlsnAAAC HTTP/1.1" 200 -
2025-06-12 20:25:36,654 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:36] "GET /socket.io/?EIO=4&transport=polling&t=PTbBFlP&sid=UeQ1JAi0S_yRFlsnAAAC HTTP/1.1" 200 -
2025-06-12 20:25:37,056 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:37] "[35m[1mGET /recordings HTTP/1.1[0m" 500 -
2025-06-12 20:25:37,087 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:37] "GET /socket.io/?EIO=4&transport=websocket&sid=UeQ1JAi0S_yRFlsnAAAC HTTP/1.1" 200 -
2025-06-12 20:25:37,305 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:37] "GET /recordings?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-12 20:25:37,400 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:37] "GET /recordings?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-12 20:25:37,439 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:37] "GET /recordings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-12 20:25:37,763 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:37] "[36mGET /recordings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:25:40,681 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:40] "GET /socket.io/?EIO=4&transport=polling&t=PTbBGp3 HTTP/1.1" 200 -
2025-06-12 20:25:41,040 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:41] "POST /socket.io/?EIO=4&transport=polling&t=PTbBGpg&sid=4DjJ4n8POGhoxSDvAAAE HTTP/1.1" 200 -
2025-06-12 20:25:41,040 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:41] "GET /socket.io/?EIO=4&transport=polling&t=PTbBGph&sid=4DjJ4n8POGhoxSDvAAAE HTTP/1.1" 200 -
2025-06-12 20:25:41,290 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:41] "GET /socket.io/?EIO=4&transport=polling&t=PTbBGur&sid=4DjJ4n8POGhoxSDvAAAE HTTP/1.1" 200 -
2025-06-12 20:25:41,358 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:41] "POST /socket.io/?EIO=4&transport=polling&t=PTbBGut&sid=4DjJ4n8POGhoxSDvAAAE HTTP/1.1" 200 -
2025-06-12 20:25:42,003 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:42] "[35m[1mGET /events HTTP/1.1[0m" 500 -
2025-06-12 20:25:42,034 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:42] "GET /socket.io/?EIO=4&transport=websocket&sid=4DjJ4n8POGhoxSDvAAAE HTTP/1.1" 200 -
2025-06-12 20:25:42,352 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:42] "GET /events?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-12 20:25:42,352 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:42] "GET /events?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-12 20:25:42,619 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:42] "GET /events?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-12 20:25:42,681 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:42] "[36mGET /events?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:25:44,153 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:44] "GET /socket.io/?EIO=4&transport=polling&t=PTbBHfJ HTTP/1.1" 200 -
2025-06-12 20:25:44,496 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:44] "POST /socket.io/?EIO=4&transport=polling&t=PTbBHfv&sid=uRAQfTKGClb51pFCAAAG HTTP/1.1" 200 -
2025-06-12 20:25:44,498 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:44] "GET /socket.io/?EIO=4&transport=polling&t=PTbBHfv.0&sid=uRAQfTKGClb51pFCAAAG HTTP/1.1" 200 -
2025-06-12 20:25:44,759 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:44] "GET /socket.io/?EIO=4&transport=polling&t=PTbBHkt&sid=uRAQfTKGClb51pFCAAAG HTTP/1.1" 200 -
2025-06-12 20:25:45,649 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:45] "[35m[1mGET /settings HTTP/1.1[0m" 500 -
2025-06-12 20:25:45,683 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:45] "GET /socket.io/?EIO=4&transport=websocket&sid=uRAQfTKGClb51pFCAAAG HTTP/1.1" 200 -
2025-06-12 20:25:46,001 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:46] "GET /settings?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-12 20:25:46,001 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:46] "GET /settings?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-12 20:25:46,268 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:46] "GET /settings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-12 20:25:46,332 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:46] "[36mGET /settings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:25:47,861 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:47] "GET /socket.io/?EIO=4&transport=polling&t=PTbBIZF HTTP/1.1" 200 -
2025-06-12 20:25:48,197 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:48] "POST /socket.io/?EIO=4&transport=polling&t=PTbBIZX&sid=O0YqGs7_Nu3-hjvAAAAI HTTP/1.1" 200 -
2025-06-12 20:25:48,197 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:48] "GET /socket.io/?EIO=4&transport=polling&t=PTbBIZY&sid=O0YqGs7_Nu3-hjvAAAAI HTTP/1.1" 200 -
2025-06-12 20:25:48,443 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:48] "GET /socket.io/?EIO=4&transport=polling&t=PTbBIeg&sid=O0YqGs7_Nu3-hjvAAAAI HTTP/1.1" 200 -
2025-06-12 20:25:48,512 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:25:48] "POST /socket.io/?EIO=4&transport=polling&t=PTbBIei&sid=O0YqGs7_Nu3-hjvAAAAI HTTP/1.1" 200 -
2025-06-12 20:27:22,221 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:22] "[33mPOST /api/cameras/test HTTP/1.1[0m" 404 -
2025-06-12 20:27:28,755 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:28] "[33mPOST /api/cameras/test HTTP/1.1[0m" 404 -
2025-06-12 20:27:30,230 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:30] "[33mPOST /api/cameras/test HTTP/1.1[0m" 404 -
2025-06-12 20:27:30,496 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:30] "[33mPOST /api/cameras/test HTTP/1.1[0m" 404 -
2025-06-12 20:27:42,499 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:42] "POST /api/cameras HTTP/1.1" 200 -
2025-06-12 20:27:42,829 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:42] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:27:42,848 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:42] "GET /socket.io/?EIO=4&transport=websocket&sid=O0YqGs7_Nu3-hjvAAAAI HTTP/1.1" 200 -
2025-06-12 20:27:43,079 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:43] "GET /socket.io/?EIO=4&transport=polling&t=PTbBkeA HTTP/1.1" 200 -
2025-06-12 20:27:43,178 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:43] "POST /socket.io/?EIO=4&transport=polling&t=PTbBkhj&sid=XmDwhGS-6m4dbUNaAAAK HTTP/1.1" 200 -
2025-06-12 20:27:43,396 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:43] "GET /socket.io/?EIO=4&transport=polling&t=PTbBkhk&sid=XmDwhGS-6m4dbUNaAAAK HTTP/1.1" 200 -
2025-06-12 20:27:43,409 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:43] "POST /socket.io/?EIO=4&transport=polling&t=PTbBkmg.0&sid=XmDwhGS-6m4dbUNaAAAK HTTP/1.1" 200 -
2025-06-12 20:27:43,713 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:43] "GET /socket.io/?EIO=4&transport=polling&t=PTbBkmg&sid=XmDwhGS-6m4dbUNaAAAK HTTP/1.1" 200 -
2025-06-12 20:27:50,473 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:50] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:27:50,481 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:50] "GET /socket.io/?EIO=4&transport=websocket&sid=XmDwhGS-6m4dbUNaAAAK HTTP/1.1" 200 -
2025-06-12 20:27:50,740 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:50] "GET /socket.io/?EIO=4&transport=polling&t=PTbBmVT HTTP/1.1" 200 -
2025-06-12 20:27:50,807 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:50] "POST /socket.io/?EIO=4&transport=polling&t=PTbBmZQ&sid=PfGyuDOpycfzHiJiAAAM HTTP/1.1" 200 -
2025-06-12 20:27:51,055 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:51] "GET /socket.io/?EIO=4&transport=polling&t=PTbBmZQ.0&sid=PfGyuDOpycfzHiJiAAAM HTTP/1.1" 200 -
2025-06-12 20:27:51,067 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:51] "POST /socket.io/?EIO=4&transport=polling&t=PTbBmeK.0&sid=PfGyuDOpycfzHiJiAAAM HTTP/1.1" 200 -
2025-06-12 20:27:51,369 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:51] "GET /socket.io/?EIO=4&transport=polling&t=PTbBmeK&sid=PfGyuDOpycfzHiJiAAAM HTTP/1.1" 200 -
2025-06-12 20:27:53,578 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:53] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:27:53,586 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:53] "GET /socket.io/?EIO=4&transport=websocket&sid=PfGyuDOpycfzHiJiAAAM HTTP/1.1" 200 -
2025-06-12 20:27:53,829 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:53] "GET /socket.io/?EIO=4&transport=polling&t=PTbBnF_ HTTP/1.1" 200 -
2025-06-12 20:27:53,911 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:53] "POST /socket.io/?EIO=4&transport=polling&t=PTbBnJi&sid=Z4jCOChPbI_hC7cAAAAO HTTP/1.1" 200 -
2025-06-12 20:27:54,142 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:54] "GET /socket.io/?EIO=4&transport=polling&t=PTbBnJi.0&sid=Z4jCOChPbI_hC7cAAAAO HTTP/1.1" 200 -
2025-06-12 20:27:54,156 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:54] "POST /socket.io/?EIO=4&transport=polling&t=PTbBnOa.0&sid=Z4jCOChPbI_hC7cAAAAO HTTP/1.1" 200 -
2025-06-12 20:27:54,458 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:27:54] "GET /socket.io/?EIO=4&transport=polling&t=PTbBnOa&sid=Z4jCOChPbI_hC7cAAAAO HTTP/1.1" 200 -
2025-06-12 20:28:08,374 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:08] "GET / HTTP/1.1" 200 -
2025-06-12 20:28:08,412 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:08] "GET /socket.io/?EIO=4&transport=websocket&sid=Z4jCOChPbI_hC7cAAAAO HTTP/1.1" 200 -
2025-06-12 20:28:08,639 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:08] "GET /socket.io/?EIO=4&transport=polling&t=PTbBqtJ HTTP/1.1" 200 -
2025-06-12 20:28:08,717 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:08] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:28:08,720 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:08] "POST /socket.io/?EIO=4&transport=polling&t=PTbBqx6&sid=xmu8wizMWD58m4LtAAAQ HTTP/1.1" 200 -
2025-06-12 20:28:08,954 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:08] "GET /socket.io/?EIO=4&transport=polling&t=PTbBqx6.0&sid=xmu8wizMWD58m4LtAAAQ HTTP/1.1" 200 -
2025-06-12 20:28:08,968 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:08] "POST /socket.io/?EIO=4&transport=polling&t=PTbBr01&sid=xmu8wizMWD58m4LtAAAQ HTTP/1.1" 200 -
2025-06-12 20:28:09,268 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:09] "GET /socket.io/?EIO=4&transport=polling&t=PTbBr00&sid=xmu8wizMWD58m4LtAAAQ HTTP/1.1" 200 -
2025-06-12 20:28:38,721 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:38] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:28:44,984 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:44] "GET / HTTP/1.1" 200 -
2025-06-12 20:28:44,992 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:44] "GET /socket.io/?EIO=4&transport=websocket&sid=xmu8wizMWD58m4LtAAAQ HTTP/1.1" 200 -
2025-06-12 20:28:45,317 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:45] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:28:45,317 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:45] "GET /socket.io/?EIO=4&transport=polling&t=PTbBzpB HTTP/1.1" 200 -
2025-06-12 20:28:45,588 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:45] "POST /socket.io/?EIO=4&transport=polling&t=PTbBzuB&sid=re4OHtqYq5t4aoAgAAAS HTTP/1.1" 200 -
2025-06-12 20:28:45,629 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:45] "GET /socket.io/?EIO=4&transport=polling&t=PTbBzuC&sid=re4OHtqYq5t4aoAgAAAS HTTP/1.1" 200 -
2025-06-12 20:28:45,642 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:45] "POST /socket.io/?EIO=4&transport=polling&t=PTbBzz4&sid=re4OHtqYq5t4aoAgAAAS HTTP/1.1" 200 -
2025-06-12 20:28:45,945 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:45] "GET /socket.io/?EIO=4&transport=polling&t=PTbBzz3&sid=re4OHtqYq5t4aoAgAAAS HTTP/1.1" 200 -
2025-06-12 20:28:47,062 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:47] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:28:47,092 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:47] "GET /socket.io/?EIO=4&transport=websocket&sid=re4OHtqYq5t4aoAgAAAS HTTP/1.1" 200 -
2025-06-12 20:28:47,330 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:47] "GET /socket.io/?EIO=4&transport=polling&t=PTbB-Jc HTTP/1.1" 200 -
2025-06-12 20:28:47,395 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:47] "POST /socket.io/?EIO=4&transport=polling&t=PTbB-Ne&sid=DfHcThYZiBqeUAUDAAAU HTTP/1.1" 200 -
2025-06-12 20:28:47,644 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:47] "GET /socket.io/?EIO=4&transport=polling&t=PTbB-Ne.0&sid=DfHcThYZiBqeUAUDAAAU HTTP/1.1" 200 -
2025-06-12 20:28:47,656 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:47] "POST /socket.io/?EIO=4&transport=polling&t=PTbB-SY.0&sid=DfHcThYZiBqeUAUDAAAU HTTP/1.1" 200 -
2025-06-12 20:28:47,957 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:47] "GET /socket.io/?EIO=4&transport=polling&t=PTbB-SY&sid=DfHcThYZiBqeUAUDAAAU HTTP/1.1" 200 -
2025-06-12 20:28:57,617 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:57] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:28:57,624 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:57] "GET /socket.io/?EIO=4&transport=websocket&sid=DfHcThYZiBqeUAUDAAAU HTTP/1.1" 200 -
2025-06-12 20:28:57,884 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:57] "GET /socket.io/?EIO=4&transport=polling&t=PTbC0uZ HTTP/1.1" 200 -
2025-06-12 20:28:57,951 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:57] "POST /socket.io/?EIO=4&transport=polling&t=PTbC0yY&sid=pdpz8JSvafpqweL6AAAW HTTP/1.1" 200 -
2025-06-12 20:28:58,200 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:58] "GET /socket.io/?EIO=4&transport=polling&t=PTbC0yY.0&sid=pdpz8JSvafpqweL6AAAW HTTP/1.1" 200 -
2025-06-12 20:28:58,212 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:58] "POST /socket.io/?EIO=4&transport=polling&t=PTbC11U&sid=pdpz8JSvafpqweL6AAAW HTTP/1.1" 200 -
2025-06-12 20:28:58,516 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:28:58] "GET /socket.io/?EIO=4&transport=polling&t=PTbC11T&sid=pdpz8JSvafpqweL6AAAW HTTP/1.1" 200 -
2025-06-12 20:29:00,510 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:00] "GET / HTTP/1.1" 200 -
2025-06-12 20:29:00,535 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:00] "GET /socket.io/?EIO=4&transport=websocket&sid=pdpz8JSvafpqweL6AAAW HTTP/1.1" 200 -
2025-06-12 20:29:00,748 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:00] "GET /socket.io/?EIO=4&transport=polling&t=PTbC1bm HTTP/1.1" 200 -
2025-06-12 20:29:00,857 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:00] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:29:00,860 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:00] "POST /socket.io/?EIO=4&transport=polling&t=PTbC1fI&sid=zQtMAX5-nypi8HUaAAAY HTTP/1.1" 200 -
2025-06-12 20:29:01,062 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:01] "GET /socket.io/?EIO=4&transport=polling&t=PTbC1fJ&sid=zQtMAX5-nypi8HUaAAAY HTTP/1.1" 200 -
2025-06-12 20:29:01,077 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:01] "POST /socket.io/?EIO=4&transport=polling&t=PTbC1kC.0&sid=zQtMAX5-nypi8HUaAAAY HTTP/1.1" 200 -
2025-06-12 20:29:01,378 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:01] "GET /socket.io/?EIO=4&transport=polling&t=PTbC1kC&sid=zQtMAX5-nypi8HUaAAAY HTTP/1.1" 200 -
2025-06-12 20:29:13,018 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:13] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:29:13,051 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:13] "GET /socket.io/?EIO=4&transport=websocket&sid=zQtMAX5-nypi8HUaAAAY HTTP/1.1" 200 -
2025-06-12 20:29:13,283 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbC4fC HTTP/1.1" 200 -
2025-06-12 20:29:13,364 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:13] "POST /socket.io/?EIO=4&transport=polling&t=PTbC4jA&sid=XkBPYu2hI4KzwhgpAAAa HTTP/1.1" 200 -
2025-06-12 20:29:13,600 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbC4jA.0&sid=XkBPYu2hI4KzwhgpAAAa HTTP/1.1" 200 -
2025-06-12 20:29:13,613 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:13] "POST /socket.io/?EIO=4&transport=polling&t=PTbC4o7&sid=XkBPYu2hI4KzwhgpAAAa HTTP/1.1" 200 -
2025-06-12 20:29:13,914 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:29:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbC4o6&sid=XkBPYu2hI4KzwhgpAAAa HTTP/1.1" 200 -
2025-06-12 20:31:33,465 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:33] "GET / HTTP/1.1" 200 -
2025-06-12 20:31:33,498 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:33] "GET /socket.io/?EIO=4&transport=websocket&sid=XkBPYu2hI4KzwhgpAAAa HTTP/1.1" 200 -
2025-06-12 20:31:33,727 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:33] "GET /socket.io/?EIO=4&transport=polling&t=PTbCcxn HTTP/1.1" 200 -
2025-06-12 20:31:33,807 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:33] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:31:33,810 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:33] "POST /socket.io/?EIO=4&transport=polling&t=PTbCc_d&sid=iTJJkIrlQE5PwDNxAAAc HTTP/1.1" 200 -
2025-06-12 20:31:34,043 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:34] "GET /socket.io/?EIO=4&transport=polling&t=PTbCc_e&sid=iTJJkIrlQE5PwDNxAAAc HTTP/1.1" 200 -
2025-06-12 20:31:34,056 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:34] "POST /socket.io/?EIO=4&transport=polling&t=PTbCd4X.0&sid=iTJJkIrlQE5PwDNxAAAc HTTP/1.1" 200 -
2025-06-12 20:31:34,355 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:34] "GET /socket.io/?EIO=4&transport=polling&t=PTbCd4X&sid=iTJJkIrlQE5PwDNxAAAc HTTP/1.1" 200 -
2025-06-12 20:31:57,753 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:57] "[35m[1mGET /settings HTTP/1.1[0m" 500 -
2025-06-12 20:31:57,780 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:57] "GET /socket.io/?EIO=4&transport=websocket&sid=iTJJkIrlQE5PwDNxAAAc HTTP/1.1" 200 -
2025-06-12 20:31:58,004 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:58] "[36mGET /settings?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:31:58,096 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:58] "[36mGET /settings?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:31:58,112 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:31:58] "[36mGET /settings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:32:01,337 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:01] "GET /socket.io/?EIO=4&transport=polling&t=PTbCjft HTTP/1.1" 200 -
2025-06-12 20:32:01,594 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:01] "POST /socket.io/?EIO=4&transport=polling&t=PTbCjl0&sid=akjIMZkATHvffnbcAAAe HTTP/1.1" 200 -
2025-06-12 20:32:01,650 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:01] "GET /socket.io/?EIO=4&transport=polling&t=PTbCjl1&sid=akjIMZkATHvffnbcAAAe HTTP/1.1" 200 -
2025-06-12 20:32:01,665 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:01] "POST /socket.io/?EIO=4&transport=polling&t=PTbCjpu.0&sid=akjIMZkATHvffnbcAAAe HTTP/1.1" 200 -
2025-06-12 20:32:01,963 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:01] "GET /socket.io/?EIO=4&transport=polling&t=PTbCjpu&sid=akjIMZkATHvffnbcAAAe HTTP/1.1" 200 -
2025-06-12 20:32:02,811 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:02] "[35m[1mGET /events HTTP/1.1[0m" 500 -
2025-06-12 20:32:02,838 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:02] "GET /socket.io/?EIO=4&transport=websocket&sid=akjIMZkATHvffnbcAAAe HTTP/1.1" 200 -
2025-06-12 20:32:03,060 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:03] "[36mGET /events?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:32:03,154 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:03] "[36mGET /events?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:32:03,169 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:03] "[36mGET /events?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:32:06,722 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:06] "GET /socket.io/?EIO=4&transport=polling&t=PTbCk-2 HTTP/1.1" 200 -
2025-06-12 20:32:06,977 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:06] "POST /socket.io/?EIO=4&transport=polling&t=PTbCl3B&sid=XyAsB7OE35_OEldYAAAg HTTP/1.1" 200 -
2025-06-12 20:32:07,036 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:07] "GET /socket.io/?EIO=4&transport=polling&t=PTbCl3B.0&sid=XyAsB7OE35_OEldYAAAg HTTP/1.1" 200 -
2025-06-12 20:32:07,050 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:07] "POST /socket.io/?EIO=4&transport=polling&t=PTbCl82.0&sid=XyAsB7OE35_OEldYAAAg HTTP/1.1" 200 -
2025-06-12 20:32:07,349 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:07] "GET /socket.io/?EIO=4&transport=polling&t=PTbCl82&sid=XyAsB7OE35_OEldYAAAg HTTP/1.1" 200 -
2025-06-12 20:32:08,760 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:08] "[35m[1mGET /recordings HTTP/1.1[0m" 500 -
2025-06-12 20:32:08,788 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:08] "GET /socket.io/?EIO=4&transport=websocket&sid=XyAsB7OE35_OEldYAAAg HTTP/1.1" 200 -
2025-06-12 20:32:09,008 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:09] "[36mGET /recordings?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:32:09,104 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:09] "[36mGET /recordings?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:32:09,119 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:09] "[36mGET /recordings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:32:11,335 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:11] "GET /socket.io/?EIO=4&transport=polling&t=PTbCm6E HTTP/1.1" 200 -
2025-06-12 20:32:11,605 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:11] "POST /socket.io/?EIO=4&transport=polling&t=PTbCmBD&sid=XASrHTEKESpW5YCwAAAi HTTP/1.1" 200 -
2025-06-12 20:32:11,650 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:11] "GET /socket.io/?EIO=4&transport=polling&t=PTbCmBE&sid=XASrHTEKESpW5YCwAAAi HTTP/1.1" 200 -
2025-06-12 20:32:11,663 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:11] "POST /socket.io/?EIO=4&transport=polling&t=PTbCmG8.0&sid=XASrHTEKESpW5YCwAAAi HTTP/1.1" 200 -
2025-06-12 20:32:11,960 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:11] "GET /socket.io/?EIO=4&transport=polling&t=PTbCmG8&sid=XASrHTEKESpW5YCwAAAi HTTP/1.1" 200 -
2025-06-12 20:32:12,454 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:12] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:32:12,494 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:12] "GET /socket.io/?EIO=4&transport=websocket&sid=XASrHTEKESpW5YCwAAAi HTTP/1.1" 200 -
2025-06-12 20:32:12,707 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:12] "GET /socket.io/?EIO=4&transport=polling&t=PTbCmT3 HTTP/1.1" 200 -
2025-06-12 20:32:12,805 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:12] "POST /socket.io/?EIO=4&transport=polling&t=PTbCmWg&sid=4dLaLW_2vTak_2G0AAAk HTTP/1.1" 200 -
2025-06-12 20:32:13,021 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbCmWg.0&sid=4dLaLW_2vTak_2G0AAAk HTTP/1.1" 200 -
2025-06-12 20:32:13,034 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:13] "POST /socket.io/?EIO=4&transport=polling&t=PTbCmbZ.0&sid=4dLaLW_2vTak_2G0AAAk HTTP/1.1" 200 -
2025-06-12 20:32:13,336 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbCmbZ&sid=4dLaLW_2vTak_2G0AAAk HTTP/1.1" 200 -
2025-06-12 20:32:14,741 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:14] "[35m[1mGET /recordings HTTP/1.1[0m" 500 -
2025-06-12 20:32:14,768 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:14] "GET /socket.io/?EIO=4&transport=websocket&sid=4dLaLW_2vTak_2G0AAAk HTTP/1.1" 200 -
2025-06-12 20:32:14,991 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:14] "[36mGET /recordings?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:32:15,085 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:15] "[36mGET /recordings?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:32:15,097 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:15] "[36mGET /recordings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:32:29,455 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:29] "GET /socket.io/?EIO=4&transport=polling&t=PTbCqXM HTTP/1.1" 200 -
2025-06-12 20:32:29,725 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:29] "POST /socket.io/?EIO=4&transport=polling&t=PTbCqcL&sid=AOYVW5s6O366FhXzAAAm HTTP/1.1" 200 -
2025-06-12 20:32:29,769 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:29] "GET /socket.io/?EIO=4&transport=polling&t=PTbCqcM&sid=AOYVW5s6O366FhXzAAAm HTTP/1.1" 200 -
2025-06-12 20:32:29,782 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:29] "POST /socket.io/?EIO=4&transport=polling&t=PTbCqhF&sid=AOYVW5s6O366FhXzAAAm HTTP/1.1" 200 -
2025-06-12 20:32:30,081 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:30] "GET /socket.io/?EIO=4&transport=polling&t=PTbCqhE&sid=AOYVW5s6O366FhXzAAAm HTTP/1.1" 200 -
2025-06-12 20:32:31,636 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:31] "[35m[1mGET /events HTTP/1.1[0m" 500 -
2025-06-12 20:32:31,663 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:31] "GET /socket.io/?EIO=4&transport=websocket&sid=AOYVW5s6O366FhXzAAAm HTTP/1.1" 200 -
2025-06-12 20:32:31,886 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:31] "[36mGET /events?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:32:31,980 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:31] "[36mGET /events?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:32:31,993 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:31] "[36mGET /events?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:32:48,233 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:48] "GET /socket.io/?EIO=4&transport=polling&t=PTbCv6d HTTP/1.1" 200 -
2025-06-12 20:32:48,488 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:48] "POST /socket.io/?EIO=4&transport=polling&t=PTbCvBm&sid=E_lmJtLKs85ykAthAAAo HTTP/1.1" 200 -
2025-06-12 20:32:48,547 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:48] "GET /socket.io/?EIO=4&transport=polling&t=PTbCvBm.0&sid=E_lmJtLKs85ykAthAAAo HTTP/1.1" 200 -
2025-06-12 20:32:48,561 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:48] "POST /socket.io/?EIO=4&transport=polling&t=PTbCvGg&sid=E_lmJtLKs85ykAthAAAo HTTP/1.1" 200 -
2025-06-12 20:32:48,860 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:48] "GET /socket.io/?EIO=4&transport=polling&t=PTbCvGf&sid=E_lmJtLKs85ykAthAAAo HTTP/1.1" 200 -
2025-06-12 20:32:49,768 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:49] "[35m[1mGET /settings HTTP/1.1[0m" 500 -
2025-06-12 20:32:49,795 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:49] "GET /socket.io/?EIO=4&transport=websocket&sid=E_lmJtLKs85ykAthAAAo HTTP/1.1" 200 -
2025-06-12 20:32:50,015 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:50] "[36mGET /settings?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:32:50,109 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:50] "[36mGET /settings?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:32:50,124 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:32:50] "[36mGET /settings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:33:06,204 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:06] "GET /socket.io/?EIO=4&transport=polling&t=PTbCzVO HTTP/1.1" 200 -
2025-06-12 20:33:06,457 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:06] "POST /socket.io/?EIO=4&transport=polling&t=PTbCzaY&sid=Q1o97A7brwBhZ8esAAAq HTTP/1.1" 200 -
2025-06-12 20:33:06,530 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:06] "GET /socket.io/?EIO=4&transport=polling&t=PTbCzaZ&sid=Q1o97A7brwBhZ8esAAAq HTTP/1.1" 200 -
2025-06-12 20:33:06,545 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:06] "POST /socket.io/?EIO=4&transport=polling&t=PTbCzfe&sid=Q1o97A7brwBhZ8esAAAq HTTP/1.1" 200 -
2025-06-12 20:33:06,845 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:06] "GET /socket.io/?EIO=4&transport=polling&t=PTbCzfd&sid=Q1o97A7brwBhZ8esAAAq HTTP/1.1" 200 -
2025-06-12 20:33:08,133 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:08] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:33:08,153 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:08] "GET /socket.io/?EIO=4&transport=websocket&sid=Q1o97A7brwBhZ8esAAAq HTTP/1.1" 200 -
2025-06-12 20:33:08,390 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:08] "GET /socket.io/?EIO=4&transport=polling&t=PTbC-34 HTTP/1.1" 200 -
2025-06-12 20:33:08,487 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:08] "POST /socket.io/?EIO=4&transport=polling&t=PTbC-6i&sid=P2gclXGdXQNu22hVAAAs HTTP/1.1" 200 -
2025-06-12 20:33:08,705 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:08] "GET /socket.io/?EIO=4&transport=polling&t=PTbC-6j&sid=P2gclXGdXQNu22hVAAAs HTTP/1.1" 200 -
2025-06-12 20:33:08,718 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:08] "POST /socket.io/?EIO=4&transport=polling&t=PTbC-Bd&sid=P2gclXGdXQNu22hVAAAs HTTP/1.1" 200 -
2025-06-12 20:33:09,018 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:09] "GET /socket.io/?EIO=4&transport=polling&t=PTbC-Bc&sid=P2gclXGdXQNu22hVAAAs HTTP/1.1" 200 -
2025-06-12 20:33:11,295 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:11] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:33:11,302 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:11] "GET /socket.io/?EIO=4&transport=websocket&sid=P2gclXGdXQNu22hVAAAs HTTP/1.1" 200 -
2025-06-12 20:33:11,557 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:11] "GET /socket.io/?EIO=4&transport=polling&t=PTbC-qJ HTTP/1.1" 200 -
2025-06-12 20:33:11,638 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:11] "POST /socket.io/?EIO=4&transport=polling&t=PTbC-uB&sid=74EAZd6sp0Hyhbi2AAAu HTTP/1.1" 200 -
2025-06-12 20:33:11,870 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:11] "GET /socket.io/?EIO=4&transport=polling&t=PTbC-uB.0&sid=74EAZd6sp0Hyhbi2AAAu HTTP/1.1" 200 -
2025-06-12 20:33:11,883 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:11] "POST /socket.io/?EIO=4&transport=polling&t=PTbC-z4.0&sid=74EAZd6sp0Hyhbi2AAAu HTTP/1.1" 200 -
2025-06-12 20:33:12,183 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:12] "GET /socket.io/?EIO=4&transport=polling&t=PTbC-z4&sid=74EAZd6sp0Hyhbi2AAAu HTTP/1.1" 200 -
2025-06-12 20:33:13,317 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:13] "GET / HTTP/1.1" 200 -
2025-06-12 20:33:13,342 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:13] "GET /socket.io/?EIO=4&transport=websocket&sid=74EAZd6sp0Hyhbi2AAAu HTTP/1.1" 200 -
2025-06-12 20:33:13,563 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbC_Jt HTTP/1.1" 200 -
2025-06-12 20:33:13,657 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:13] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:33:13,660 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:13] "POST /socket.io/?EIO=4&transport=polling&t=PTbC_NY&sid=y_jzTEfVUTVIQ0WTAAAw HTTP/1.1" 200 -
2025-06-12 20:33:13,876 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbC_NZ&sid=y_jzTEfVUTVIQ0WTAAAw HTTP/1.1" 200 -
2025-06-12 20:33:13,890 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:13] "POST /socket.io/?EIO=4&transport=polling&t=PTbC_SQ&sid=y_jzTEfVUTVIQ0WTAAAw HTTP/1.1" 200 -
2025-06-12 20:33:14,187 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:14] "GET /socket.io/?EIO=4&transport=polling&t=PTbC_SP&sid=y_jzTEfVUTVIQ0WTAAAw HTTP/1.1" 200 -
2025-06-12 20:33:43,643 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:33:43] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:34:13,346 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:34:13] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:34:43,659 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:34:43] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:34:51,136 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:34:51] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:34:51,194 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:34:51] "GET /socket.io/?EIO=4&transport=websocket&sid=y_jzTEfVUTVIQ0WTAAAw HTTP/1.1" 200 -
2025-06-12 20:34:51,507 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:34:51] "GET /socket.io/?EIO=4&transport=polling&t=PTbDNCg HTTP/1.1" 200 -
2025-06-12 20:34:51,762 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:34:51] "POST /socket.io/?EIO=4&transport=polling&t=PTbDNHw&sid=HWOwlGwf_UUIcMiWAAAy HTTP/1.1" 200 -
2025-06-12 20:34:51,821 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:34:51] "GET /socket.io/?EIO=4&transport=polling&t=PTbDNHw.0&sid=HWOwlGwf_UUIcMiWAAAy HTTP/1.1" 200 -
2025-06-12 20:34:51,834 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:34:51] "POST /socket.io/?EIO=4&transport=polling&t=PTbDNMp.0&sid=HWOwlGwf_UUIcMiWAAAy HTTP/1.1" 200 -
2025-06-12 20:34:52,149 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:34:52] "GET /socket.io/?EIO=4&transport=polling&t=PTbDNMp&sid=HWOwlGwf_UUIcMiWAAAy HTTP/1.1" 200 -
2025-06-12 20:35:13,537 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:35:13] "[33mPOST /api/cameras/detect-channels HTTP/1.1[0m" 404 -
2025-06-12 20:35:18,625 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:35:18] "[33mPOST /api/cameras/detect-channels HTTP/1.1[0m" 404 -
2025-06-12 20:35:22,214 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\utils\\camera_discovery.py', reloading
2025-06-12 20:35:22,216 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\utils\\camera_discovery.py', reloading
2025-06-12 20:35:22,216 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\utils\\camera_discovery.py', reloading
2025-06-12 20:35:22,538 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:35:23,765 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:35:23,771 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:35:23,774 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:35:24,329 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:35:24] "GET /socket.io/?EIO=4&transport=polling&t=PTbDVDf HTTP/1.1" 200 -
2025-06-12 20:35:24,583 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:35:24] "POST /socket.io/?EIO=4&transport=polling&t=PTbDVIm&sid=UJ2pfjKMJ9iK_gNrAAAA HTTP/1.1" 200 -
2025-06-12 20:35:24,642 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:35:24] "GET /socket.io/?EIO=4&transport=polling&t=PTbDVIm.0&sid=UJ2pfjKMJ9iK_gNrAAAA HTTP/1.1" 200 -
2025-06-12 20:35:24,650 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:35:24] "GET /socket.io/?EIO=4&transport=polling&t=PTbDVNd&sid=UJ2pfjKMJ9iK_gNrAAAA HTTP/1.1" 200 -
2025-06-12 20:35:52,007 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:35:52,009 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:35:52,009 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:35:52,262 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\Python313\\Lib\\urllib\\parse.py', reloading
2025-06-12 20:35:52,326 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cv2\\typing\\__init__.py', reloading
2025-06-12 20:35:52,695 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:35:53,962 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:35:53,969 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:35:53,971 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:35:54,378 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:35:54] "GET /socket.io/?EIO=4&transport=polling&t=PTbDcZI HTTP/1.1" 200 -
2025-06-12 20:35:54,646 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:35:54] "POST /socket.io/?EIO=4&transport=polling&t=PTbDceH&sid=aNiwfWy5aXxZGoxMAAAA HTTP/1.1" 200 -
2025-06-12 20:35:54,705 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:35:54] "GET /socket.io/?EIO=4&transport=polling&t=PTbDceI&sid=aNiwfWy5aXxZGoxMAAAA HTTP/1.1" 200 -
2025-06-12 20:35:54,717 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:35:54] "GET /socket.io/?EIO=4&transport=polling&t=PTbDcjO&sid=aNiwfWy5aXxZGoxMAAAA HTTP/1.1" 200 -
2025-06-12 20:36:24,978 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\test_discovery.py', reloading
2025-06-12 20:36:24,980 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\test_discovery.py', reloading
2025-06-12 20:36:24,980 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\test_discovery.py', reloading
2025-06-12 20:36:25,537 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:36:26,661 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:36:26,668 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:36:26,670 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:36:26,700 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:26] "GET /socket.io/?EIO=4&transport=polling&t=PTbDkSF HTTP/1.1" 200 -
2025-06-12 20:36:27,020 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:27] "POST /socket.io/?EIO=4&transport=polling&t=PTbDkXI&sid=0AWl4Q_MPURvbLNgAAAA HTTP/1.1" 200 -
2025-06-12 20:36:27,020 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:27] "GET /socket.io/?EIO=4&transport=polling&t=PTbDkXJ&sid=0AWl4Q_MPURvbLNgAAAA HTTP/1.1" 200 -
2025-06-12 20:36:27,284 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:27] "GET /socket.io/?EIO=4&transport=polling&t=PTbDkcI&sid=0AWl4Q_MPURvbLNgAAAA HTTP/1.1" 200 -
2025-06-12 20:36:32,696 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:32] "POST /api/cameras HTTP/1.1" 200 -
2025-06-12 20:36:33,041 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:33] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:36:33,064 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:33] "GET /socket.io/?EIO=4&transport=websocket&sid=0AWl4Q_MPURvbLNgAAAA HTTP/1.1" 200 -
2025-06-12 20:36:33,290 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:33] "GET /socket.io/?EIO=4&transport=polling&t=PTbDm4r HTTP/1.1" 200 -
2025-06-12 20:36:33,385 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:33] "POST /socket.io/?EIO=4&transport=polling&t=PTbDm8G&sid=r7PlN0Dvs6P1F0dxAAAC HTTP/1.1" 200 -
2025-06-12 20:36:33,604 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:33] "GET /socket.io/?EIO=4&transport=polling&t=PTbDm8G.0&sid=r7PlN0Dvs6P1F0dxAAAC HTTP/1.1" 200 -
2025-06-12 20:36:33,618 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:33] "POST /socket.io/?EIO=4&transport=polling&t=PTbDmDA.0&sid=r7PlN0Dvs6P1F0dxAAAC HTTP/1.1" 200 -
2025-06-12 20:36:33,919 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:33] "GET /socket.io/?EIO=4&transport=polling&t=PTbDmDA&sid=r7PlN0Dvs6P1F0dxAAAC HTTP/1.1" 200 -
2025-06-12 20:36:36,686 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:36] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:36:36,694 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:36] "GET /socket.io/?EIO=4&transport=websocket&sid=r7PlN0Dvs6P1F0dxAAAC HTTP/1.1" 200 -
2025-06-12 20:36:36,932 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:36] "GET /socket.io/?EIO=4&transport=polling&t=PTbDmzY HTTP/1.1" 200 -
2025-06-12 20:36:37,030 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:37] "POST /socket.io/?EIO=4&transport=polling&t=PTbDn1B&sid=mCsuuSBdBd1ieXA4AAAE HTTP/1.1" 200 -
2025-06-12 20:36:37,247 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:37] "GET /socket.io/?EIO=4&transport=polling&t=PTbDn1B.0&sid=mCsuuSBdBd1ieXA4AAAE HTTP/1.1" 200 -
2025-06-12 20:36:37,260 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:37] "POST /socket.io/?EIO=4&transport=polling&t=PTbDn65.0&sid=mCsuuSBdBd1ieXA4AAAE HTTP/1.1" 200 -
2025-06-12 20:36:37,562 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:37] "GET /socket.io/?EIO=4&transport=polling&t=PTbDn65&sid=mCsuuSBdBd1ieXA4AAAE HTTP/1.1" 200 -
2025-06-12 20:36:40,321 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:40] "GET / HTTP/1.1" 200 -
2025-06-12 20:36:40,357 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:40] "GET /socket.io/?EIO=4&transport=websocket&sid=mCsuuSBdBd1ieXA4AAAE HTTP/1.1" 200 -
2025-06-12 20:36:40,577 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:40] "GET /socket.io/?EIO=4&transport=polling&t=PTbDnsS HTTP/1.1" 200 -
2025-06-12 20:36:40,658 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:40] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:36:40,661 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:40] "POST /socket.io/?EIO=4&transport=polling&t=PTbDnw7&sid=325b19xDvwNe-DaDAAAG HTTP/1.1" 200 -
2025-06-12 20:36:40,890 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:40] "GET /socket.io/?EIO=4&transport=polling&t=PTbDnw8&sid=325b19xDvwNe-DaDAAAG HTTP/1.1" 200 -
2025-06-12 20:36:40,903 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:40] "POST /socket.io/?EIO=4&transport=polling&t=PTbDn-_.0&sid=325b19xDvwNe-DaDAAAG HTTP/1.1" 200 -
2025-06-12 20:36:41,203 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:41] "GET /socket.io/?EIO=4&transport=polling&t=PTbDn-_&sid=325b19xDvwNe-DaDAAAG HTTP/1.1" 200 -
2025-06-12 20:36:58,624 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:58] "[35m[1mGET /settings HTTP/1.1[0m" 500 -
2025-06-12 20:36:58,651 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:58] "GET /socket.io/?EIO=4&transport=websocket&sid=325b19xDvwNe-DaDAAAG HTTP/1.1" 200 -
2025-06-12 20:36:58,899 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:58] "[36mGET /settings?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:36:58,962 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:58] "[36mGET /settings?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:36:58,977 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:36:58] "[36mGET /settings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:37:01,052 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:01] "GET /socket.io/?EIO=4&transport=polling&t=PTbDsr0 HTTP/1.1" 200 -
2025-06-12 20:37:01,320 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:01] "POST /socket.io/?EIO=4&transport=polling&t=PTbDsw3&sid=LSFYkMjAV6Xaj9bnAAAI HTTP/1.1" 200 -
2025-06-12 20:37:01,365 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:01] "GET /socket.io/?EIO=4&transport=polling&t=PTbDsw3.0&sid=LSFYkMjAV6Xaj9bnAAAI HTTP/1.1" 200 -
2025-06-12 20:37:01,380 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:01] "POST /socket.io/?EIO=4&transport=polling&t=PTbDs-y&sid=LSFYkMjAV6Xaj9bnAAAI HTTP/1.1" 200 -
2025-06-12 20:37:01,679 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:01] "GET /socket.io/?EIO=4&transport=polling&t=PTbDs-x&sid=LSFYkMjAV6Xaj9bnAAAI HTTP/1.1" 200 -
2025-06-12 20:37:02,209 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:02] "[35m[1mGET /events HTTP/1.1[0m" 500 -
2025-06-12 20:37:02,236 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:02] "GET /socket.io/?EIO=4&transport=websocket&sid=LSFYkMjAV6Xaj9bnAAAI HTTP/1.1" 200 -
2025-06-12 20:37:02,458 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:02] "[36mGET /events?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:37:02,552 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:02] "[36mGET /events?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:37:02,566 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:02] "[36mGET /events?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:37:04,511 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:04] "GET /socket.io/?EIO=4&transport=polling&t=PTbDth8 HTTP/1.1" 200 -
2025-06-12 20:37:04,780 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:04] "POST /socket.io/?EIO=4&transport=polling&t=PTbDtm5&sid=W1Fg4TCQLFE4SwDFAAAK HTTP/1.1" 200 -
2025-06-12 20:37:04,824 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:04] "GET /socket.io/?EIO=4&transport=polling&t=PTbDtm6&sid=W1Fg4TCQLFE4SwDFAAAK HTTP/1.1" 200 -
2025-06-12 20:37:04,836 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:04] "POST /socket.io/?EIO=4&transport=polling&t=PTbDtqz.0&sid=W1Fg4TCQLFE4SwDFAAAK HTTP/1.1" 200 -
2025-06-12 20:37:05,138 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:05] "GET /socket.io/?EIO=4&transport=polling&t=PTbDtqz&sid=W1Fg4TCQLFE4SwDFAAAK HTTP/1.1" 200 -
2025-06-12 20:37:05,531 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:05] "[35m[1mGET /recordings HTTP/1.1[0m" 500 -
2025-06-12 20:37:05,558 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:05] "GET /socket.io/?EIO=4&transport=websocket&sid=W1Fg4TCQLFE4SwDFAAAK HTTP/1.1" 200 -
2025-06-12 20:37:05,752 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:05] "[36mGET /recordings?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:37:05,875 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:05] "[36mGET /recordings?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:37:05,889 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:05] "[36mGET /recordings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:37:08,076 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:08] "GET /socket.io/?EIO=4&transport=polling&t=PTbDuYh HTTP/1.1" 200 -
2025-06-12 20:37:08,344 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:08] "POST /socket.io/?EIO=4&transport=polling&t=PTbDudp&sid=eT3x7f1RQdp3JEiGAAAM HTTP/1.1" 200 -
2025-06-12 20:37:08,403 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:08] "GET /socket.io/?EIO=4&transport=polling&t=PTbDudp.0&sid=eT3x7f1RQdp3JEiGAAAM HTTP/1.1" 200 -
2025-06-12 20:37:08,417 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:08] "POST /socket.io/?EIO=4&transport=polling&t=PTbDuiv&sid=eT3x7f1RQdp3JEiGAAAM HTTP/1.1" 200 -
2025-06-12 20:37:08,719 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:08] "GET /socket.io/?EIO=4&transport=polling&t=PTbDuiu&sid=eT3x7f1RQdp3JEiGAAAM HTTP/1.1" 200 -
2025-06-12 20:37:28,821 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\demo_discovery.py', reloading
2025-06-12 20:37:28,822 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\demo_discovery.py', reloading
2025-06-12 20:37:28,823 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\demo_discovery.py', reloading
2025-06-12 20:37:29,387 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:37:30,561 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:37:30,567 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:37:30,570 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:37:31,001 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:31] "GET /socket.io/?EIO=4&transport=polling&t=PTbD-8v HTTP/1.1" 200 -
2025-06-12 20:37:31,255 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:31] "POST /socket.io/?EIO=4&transport=polling&t=PTbD-E2&sid=TU725OE8CZzSysYCAAAA HTTP/1.1" 200 -
2025-06-12 20:37:31,330 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:31] "GET /socket.io/?EIO=4&transport=polling&t=PTbD-E2.0&sid=TU725OE8CZzSysYCAAAA HTTP/1.1" 200 -
2025-06-12 20:37:31,340 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:31] "GET /socket.io/?EIO=4&transport=polling&t=PTbD-J9&sid=TU725OE8CZzSysYCAAAA HTTP/1.1" 200 -
2025-06-12 20:37:38,077 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:37:38] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:38:07,762 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:38:07] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:38:33,729 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:38:33] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:38:33,760 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:38:33] "GET /socket.io/?EIO=4&transport=websocket&sid=TU725OE8CZzSysYCAAAA HTTP/1.1" 200 -
2025-06-12 20:38:33,975 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:38:33] "GET /socket.io/?EIO=4&transport=polling&t=PTbEDYK HTTP/1.1" 200 -
2025-06-12 20:38:34,058 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:38:34] "POST /socket.io/?EIO=4&transport=polling&t=PTbEDbz&sid=LUCsCQNsTXqoWR0PAAAC HTTP/1.1" 200 -
2025-06-12 20:38:34,287 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:38:34] "GET /socket.io/?EIO=4&transport=polling&t=PTbEDb-&sid=LUCsCQNsTXqoWR0PAAAC HTTP/1.1" 200 -
2025-06-12 20:38:34,299 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:38:34] "POST /socket.io/?EIO=4&transport=polling&t=PTbEDgr&sid=LUCsCQNsTXqoWR0PAAAC HTTP/1.1" 200 -
2025-06-12 20:38:34,601 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:38:34] "GET /socket.io/?EIO=4&transport=polling&t=PTbEDgq&sid=LUCsCQNsTXqoWR0PAAAC HTTP/1.1" 200 -
2025-06-12 20:39:30,399 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:39:30] "POST /api/cameras/test HTTP/1.1" 200 -
2025-06-12 20:39:38,139 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:39:38] "POST /api/cameras/detect-channels HTTP/1.1" 200 -
2025-06-12 20:39:52,044 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:39:52] "POST /api/cameras HTTP/1.1" 200 -
2025-06-12 20:39:52,370 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:39:52] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:39:52,393 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:39:52] "GET /socket.io/?EIO=4&transport=websocket&sid=LUCsCQNsTXqoWR0PAAAC HTTP/1.1" 200 -
2025-06-12 20:39:52,714 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:39:52] "GET /socket.io/?EIO=4&transport=polling&t=PTbEWlK HTTP/1.1" 200 -
2025-06-12 20:39:52,986 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:39:52] "POST /socket.io/?EIO=4&transport=polling&t=PTbEWqG&sid=oATwrCvRmSwfGropAAAE HTTP/1.1" 200 -
2025-06-12 20:39:53,031 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:39:53] "GET /socket.io/?EIO=4&transport=polling&t=PTbEWqH&sid=oATwrCvRmSwfGropAAAE HTTP/1.1" 200 -
2025-06-12 20:39:53,044 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:39:53] "POST /socket.io/?EIO=4&transport=polling&t=PTbEWvD&sid=oATwrCvRmSwfGropAAAE HTTP/1.1" 200 -
2025-06-12 20:39:53,344 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:39:53] "GET /socket.io/?EIO=4&transport=polling&t=PTbEWvC&sid=oATwrCvRmSwfGropAAAE HTTP/1.1" 200 -
2025-06-12 20:40:06,552 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:06] "GET / HTTP/1.1" 200 -
2025-06-12 20:40:06,592 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:06] "GET /socket.io/?EIO=4&transport=websocket&sid=oATwrCvRmSwfGropAAAE HTTP/1.1" 200 -
2025-06-12 20:40:06,809 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:06] "GET /socket.io/?EIO=4&transport=polling&t=PTbEaCp HTTP/1.1" 200 -
2025-06-12 20:40:06,902 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:06] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:40:06,905 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:06] "POST /socket.io/?EIO=4&transport=polling&t=PTbEaGV&sid=Y1lJjNHVBO15AXVrAAAG HTTP/1.1" 200 -
2025-06-12 20:40:07,121 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:07] "GET /socket.io/?EIO=4&transport=polling&t=PTbEaGW&sid=Y1lJjNHVBO15AXVrAAAG HTTP/1.1" 200 -
2025-06-12 20:40:07,134 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:07] "POST /socket.io/?EIO=4&transport=polling&t=PTbEaLN.0&sid=Y1lJjNHVBO15AXVrAAAG HTTP/1.1" 200 -
2025-06-12 20:40:07,432 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:07] "GET /socket.io/?EIO=4&transport=polling&t=PTbEaLN&sid=Y1lJjNHVBO15AXVrAAAG HTTP/1.1" 200 -
2025-06-12 20:40:16,835 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:16] "GET /recordings HTTP/1.1" 200 -
2025-06-12 20:40:16,881 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:16] "GET /socket.io/?EIO=4&transport=websocket&sid=Y1lJjNHVBO15AXVrAAAG HTTP/1.1" 200 -
2025-06-12 20:40:17,173 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:17] "POST /api/cameras/detect-channels HTTP/1.1" 200 -
2025-06-12 20:40:21,314 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:21] "[35m[1mGET /events HTTP/1.1[0m" 500 -
2025-06-12 20:40:21,697 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:21] "[36mGET /events?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:40:21,700 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:21] "[36mGET /events?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:40:21,918 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:21] "[36mGET /events?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:40:29,547 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:29] "GET / HTTP/1.1" 200 -
2025-06-12 20:40:29,899 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:29] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:40:29,899 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:29] "GET /socket.io/?EIO=4&transport=polling&t=PTbEfp- HTTP/1.1" 200 -
2025-06-12 20:40:30,151 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:30] "POST /socket.io/?EIO=4&transport=polling&t=PTbEfvH&sid=5nPDjxCo6VtiZFoOAAAI HTTP/1.1" 200 -
2025-06-12 20:40:30,212 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PTbEfvI&sid=5nPDjxCo6VtiZFoOAAAI HTTP/1.1" 200 -
2025-06-12 20:40:30,226 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:30] "POST /socket.io/?EIO=4&transport=polling&t=PTbEf-B&sid=5nPDjxCo6VtiZFoOAAAI HTTP/1.1" 200 -
2025-06-12 20:40:30,525 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PTbEf-A&sid=5nPDjxCo6VtiZFoOAAAI HTTP/1.1" 200 -
2025-06-12 20:40:39,700 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:39] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:40:39,733 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:39] "GET /socket.io/?EIO=4&transport=websocket&sid=5nPDjxCo6VtiZFoOAAAI HTTP/1.1" 200 -
2025-06-12 20:40:39,962 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:39] "GET /socket.io/?EIO=4&transport=polling&t=PTbEiId HTTP/1.1" 200 -
2025-06-12 20:40:40,044 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:40] "POST /socket.io/?EIO=4&transport=polling&t=PTbEiMY&sid=RSy8b_gzLFWOWNCgAAAK HTTP/1.1" 200 -
2025-06-12 20:40:40,278 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:40] "GET /socket.io/?EIO=4&transport=polling&t=PTbEiMY.0&sid=RSy8b_gzLFWOWNCgAAAK HTTP/1.1" 200 -
2025-06-12 20:40:40,291 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:40] "POST /socket.io/?EIO=4&transport=polling&t=PTbEiRS&sid=RSy8b_gzLFWOWNCgAAAK HTTP/1.1" 200 -
2025-06-12 20:40:40,592 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:40:40] "GET /socket.io/?EIO=4&transport=polling&t=PTbEiRR&sid=RSy8b_gzLFWOWNCgAAAK HTTP/1.1" 200 -
2025-06-12 20:41:39,701 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:39] "POST /api/cameras HTTP/1.1" 200 -
2025-06-12 20:41:40,028 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:40] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:41:40,052 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:40] "GET /socket.io/?EIO=4&transport=websocket&sid=RSy8b_gzLFWOWNCgAAAK HTTP/1.1" 200 -
2025-06-12 20:41:40,371 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:40] "GET /socket.io/?EIO=4&transport=polling&t=PTbEx1W HTTP/1.1" 200 -
2025-06-12 20:41:40,643 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:40] "POST /socket.io/?EIO=4&transport=polling&t=PTbEx6Q&sid=6YjRs5O_0MoCkLiiAAAM HTTP/1.1" 200 -
2025-06-12 20:41:40,686 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:40] "GET /socket.io/?EIO=4&transport=polling&t=PTbEx6Q.0&sid=6YjRs5O_0MoCkLiiAAAM HTTP/1.1" 200 -
2025-06-12 20:41:40,699 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:40] "POST /socket.io/?EIO=4&transport=polling&t=PTbExBK.0&sid=6YjRs5O_0MoCkLiiAAAM HTTP/1.1" 200 -
2025-06-12 20:41:41,001 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:41] "GET /socket.io/?EIO=4&transport=polling&t=PTbExBK&sid=6YjRs5O_0MoCkLiiAAAM HTTP/1.1" 200 -
2025-06-12 20:41:43,746 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:43] "GET / HTTP/1.1" 200 -
2025-06-12 20:41:43,781 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:43] "GET /socket.io/?EIO=4&transport=websocket&sid=6YjRs5O_0MoCkLiiAAAM HTTP/1.1" 200 -
2025-06-12 20:41:43,995 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:43] "GET /socket.io/?EIO=4&transport=polling&t=PTbExxT HTTP/1.1" 200 -
2025-06-12 20:41:44,089 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:44] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:41:44,092 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:44] "POST /socket.io/?EIO=4&transport=polling&t=PTbEx_3&sid=myFWI-5U-grmw5BWAAAO HTTP/1.1" 200 -
2025-06-12 20:41:44,310 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:44] "GET /socket.io/?EIO=4&transport=polling&t=PTbEx_3.0&sid=myFWI-5U-grmw5BWAAAO HTTP/1.1" 200 -
2025-06-12 20:41:44,326 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:44] "POST /socket.io/?EIO=4&transport=polling&t=PTbEy3-&sid=myFWI-5U-grmw5BWAAAO HTTP/1.1" 200 -
2025-06-12 20:41:44,623 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:44] "GET /socket.io/?EIO=4&transport=polling&t=PTbEy3z&sid=myFWI-5U-grmw5BWAAAO HTTP/1.1" 200 -
2025-06-12 20:41:54,675 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:41:54] "POST /api/cameras/test HTTP/1.1" 200 -
2025-06-12 20:42:02,006 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:02] "GET /recordings HTTP/1.1" 200 -
2025-06-12 20:42:02,036 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:02] "GET /socket.io/?EIO=4&transport=websocket&sid=myFWI-5U-grmw5BWAAAO HTTP/1.1" 200 -
2025-06-12 20:42:03,796 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:03] "[33mPOST /api/recordings/start-all HTTP/1.1[0m" 404 -
2025-06-12 20:42:06,694 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:06] "[33mPOST /api/recordings/start-all HTTP/1.1[0m" 404 -
2025-06-12 20:42:07,526 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:07] "GET /events HTTP/1.1" 200 -
2025-06-12 20:42:07,866 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:07] "[33mGET /api/events/stats HTTP/1.1[0m" 404 -
2025-06-12 20:42:11,074 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:11] "[35m[1mGET /settings HTTP/1.1[0m" 500 -
2025-06-12 20:42:11,422 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:11] "[36mGET /settings?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-12 20:42:11,422 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:11] "[36mGET /settings?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-12 20:42:11,673 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:11] "[36mGET /settings?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-12 20:42:23,239 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:23] "GET /settings HTTP/1.1" 200 -
2025-06-12 20:42:33,822 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:33] "GET / HTTP/1.1" 200 -
2025-06-12 20:42:34,087 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:34] "GET /socket.io/?EIO=4&transport=polling&t=PTbF89l HTTP/1.1" 200 -
2025-06-12 20:42:34,167 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:34] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:42:34,171 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:34] "POST /socket.io/?EIO=4&transport=polling&t=PTbF8Di&sid=rdhyZO63w10emQb_AAAQ HTTP/1.1" 200 -
2025-06-12 20:42:34,403 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:34] "GET /socket.io/?EIO=4&transport=polling&t=PTbF8Dj&sid=rdhyZO63w10emQb_AAAQ HTTP/1.1" 200 -
2025-06-12 20:42:34,417 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:34] "POST /socket.io/?EIO=4&transport=polling&t=PTbF8If&sid=rdhyZO63w10emQb_AAAQ HTTP/1.1" 200 -
2025-06-12 20:42:34,730 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:42:34] "GET /socket.io/?EIO=4&transport=polling&t=PTbF8Ie&sid=rdhyZO63w10emQb_AAAQ HTTP/1.1" 200 -
2025-06-12 20:43:04,167 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:43:04] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:43:33,845 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:43:33] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:44:04,156 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:04] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:44:13,066 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:13] "GET /settings HTTP/1.1" 200 -
2025-06-12 20:44:13,089 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:13] "GET /socket.io/?EIO=4&transport=websocket&sid=rdhyZO63w10emQb_AAAQ HTTP/1.1" 200 -
2025-06-12 20:44:15,780 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:15] "GET /settings HTTP/1.1" 200 -
2025-06-12 20:44:17,302 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:17] "GET /socket.io/?EIO=4&transport=polling&t=PTbFXQE HTTP/1.1" 200 -
2025-06-12 20:44:17,656 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:17] "POST /socket.io/?EIO=4&transport=polling&t=PTbFXQq&sid=eubw3RrJNg5VzM_kAAAS HTTP/1.1" 200 -
2025-06-12 20:44:17,656 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:17] "GET /socket.io/?EIO=4&transport=polling&t=PTbFXQr&sid=eubw3RrJNg5VzM_kAAAS HTTP/1.1" 200 -
2025-06-12 20:44:17,904 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:17] "GET /socket.io/?EIO=4&transport=polling&t=PTbFXV_&sid=eubw3RrJNg5VzM_kAAAS HTTP/1.1" 200 -
2025-06-12 20:44:17,986 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:17] "POST /socket.io/?EIO=4&transport=polling&t=PTbFXW0&sid=eubw3RrJNg5VzM_kAAAS HTTP/1.1" 200 -
2025-06-12 20:44:34,360 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:34] "GET / HTTP/1.1" 200 -
2025-06-12 20:44:34,380 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:34] "GET /socket.io/?EIO=4&transport=websocket&sid=eubw3RrJNg5VzM_kAAAS HTTP/1.1" 200 -
2025-06-12 20:44:34,713 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:34] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:44:34,714 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:34] "GET /socket.io/?EIO=4&transport=polling&t=PTbFbbV HTTP/1.1" 200 -
2025-06-12 20:44:34,983 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:34] "POST /socket.io/?EIO=4&transport=polling&t=PTbFbgX&sid=F2HbL2rMk2G942QNAAAU HTTP/1.1" 200 -
2025-06-12 20:44:35,042 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:35] "GET /socket.io/?EIO=4&transport=polling&t=PTbFbgX.0&sid=F2HbL2rMk2G942QNAAAU HTTP/1.1" 200 -
2025-06-12 20:44:35,057 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:35] "POST /socket.io/?EIO=4&transport=polling&t=PTbFblf.0&sid=F2HbL2rMk2G942QNAAAU HTTP/1.1" 200 -
2025-06-12 20:44:35,369 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:35] "GET /socket.io/?EIO=4&transport=polling&t=PTbFblf&sid=F2HbL2rMk2G942QNAAAU HTTP/1.1" 200 -
2025-06-12 20:44:36,061 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:36] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:44:36,092 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:36] "GET /socket.io/?EIO=4&transport=websocket&sid=F2HbL2rMk2G942QNAAAU HTTP/1.1" 200 -
2025-06-12 20:44:36,311 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:36] "GET /socket.io/?EIO=4&transport=polling&t=PTbFb_m HTTP/1.1" 200 -
2025-06-12 20:44:36,408 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:36] "POST /socket.io/?EIO=4&transport=polling&t=PTbFc3U&sid=1bvdBd4RTAmzPGSFAAAW HTTP/1.1" 200 -
2025-06-12 20:44:36,627 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:36] "GET /socket.io/?EIO=4&transport=polling&t=PTbFc3U.0&sid=1bvdBd4RTAmzPGSFAAAW HTTP/1.1" 200 -
2025-06-12 20:44:36,639 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:36] "POST /socket.io/?EIO=4&transport=polling&t=PTbFc8P.0&sid=1bvdBd4RTAmzPGSFAAAW HTTP/1.1" 200 -
2025-06-12 20:44:36,938 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:36] "GET /socket.io/?EIO=4&transport=polling&t=PTbFc8P&sid=1bvdBd4RTAmzPGSFAAAW HTTP/1.1" 200 -
2025-06-12 20:44:55,228 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:55] "POST /api/cameras/detect-channels HTTP/1.1" 200 -
2025-06-12 20:44:57,893 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:57] "GET /settings HTTP/1.1" 200 -
2025-06-12 20:44:57,917 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:44:57] "GET /socket.io/?EIO=4&transport=websocket&sid=1bvdBd4RTAmzPGSFAAAW HTTP/1.1" 200 -
2025-06-12 20:45:02,990 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:02] "GET /socket.io/?EIO=4&transport=polling&t=PTbFia8 HTTP/1.1" 200 -
2025-06-12 20:45:03,321 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:03] "POST /socket.io/?EIO=4&transport=polling&t=PTbFiaN&sid=2uuCSG2bhDsyDgDBAAAY HTTP/1.1" 200 -
2025-06-12 20:45:03,321 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:03] "GET /socket.io/?EIO=4&transport=polling&t=PTbFiaN.0&sid=2uuCSG2bhDsyDgDBAAAY HTTP/1.1" 200 -
2025-06-12 20:45:03,569 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:03] "GET /socket.io/?EIO=4&transport=polling&t=PTbFifV&sid=2uuCSG2bhDsyDgDBAAAY HTTP/1.1" 200 -
2025-06-12 20:45:03,637 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:03] "POST /socket.io/?EIO=4&transport=polling&t=PTbFifW&sid=2uuCSG2bhDsyDgDBAAAY HTTP/1.1" 200 -
2025-06-12 20:45:09,235 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:09] "GET / HTTP/1.1" 200 -
2025-06-12 20:45:09,271 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:09] "GET /socket.io/?EIO=4&transport=websocket&sid=2uuCSG2bhDsyDgDBAAAY HTTP/1.1" 200 -
2025-06-12 20:45:09,589 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:09] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:45:09,589 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:09] "GET /socket.io/?EIO=4&transport=polling&t=PTbFk6E HTTP/1.1" 200 -
2025-06-12 20:45:09,858 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:09] "POST /socket.io/?EIO=4&transport=polling&t=PTbFkBS&sid=Ynqgl8U-HdkjC-I7AAAa HTTP/1.1" 200 -
2025-06-12 20:45:09,901 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:09] "GET /socket.io/?EIO=4&transport=polling&t=PTbFkBS.0&sid=Ynqgl8U-HdkjC-I7AAAa HTTP/1.1" 200 -
2025-06-12 20:45:09,916 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:09] "POST /socket.io/?EIO=4&transport=polling&t=PTbFkGM.0&sid=Ynqgl8U-HdkjC-I7AAAa HTTP/1.1" 200 -
2025-06-12 20:45:10,215 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:10] "GET /socket.io/?EIO=4&transport=polling&t=PTbFkGM&sid=Ynqgl8U-HdkjC-I7AAAa HTTP/1.1" 200 -
2025-06-12 20:45:10,495 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:45:10,498 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:45:11,653 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:45:13,209 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:45:13,218 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:45:13,220 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:45:13,421 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbFl2K HTTP/1.1" 200 -
2025-06-12 20:45:13,690 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:13] "POST /socket.io/?EIO=4&transport=polling&t=PTbFl7J&sid=2x0nV9pHYddyQGFCAAAA HTTP/1.1" 200 -
2025-06-12 20:45:13,732 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbFl7J.0&sid=2x0nV9pHYddyQGFCAAAA HTTP/1.1" 200 -
2025-06-12 20:45:13,740 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbFlCA&sid=2x0nV9pHYddyQGFCAAAA HTTP/1.1" 200 -
2025-06-12 20:45:31,794 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:31] "GET /settings HTTP/1.1" 200 -
2025-06-12 20:45:31,824 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:31] "GET /socket.io/?EIO=4&transport=websocket&sid=2x0nV9pHYddyQGFCAAAA HTTP/1.1" 200 -
2025-06-12 20:45:34,919 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PTbFqMy HTTP/1.1" 200 -
2025-06-12 20:45:35,281 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:35] "POST /socket.io/?EIO=4&transport=polling&t=PTbFqNI&sid=djqbSlnPkxk0715_AAAC HTTP/1.1" 200 -
2025-06-12 20:45:35,281 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:35] "GET /socket.io/?EIO=4&transport=polling&t=PTbFqNJ&sid=djqbSlnPkxk0715_AAAC HTTP/1.1" 200 -
2025-06-12 20:45:35,533 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:35] "POST /socket.io/?EIO=4&transport=polling&t=PTbFqSw&sid=djqbSlnPkxk0715_AAAC HTTP/1.1" 200 -
2025-06-12 20:45:35,607 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:45:35] "GET /socket.io/?EIO=4&transport=polling&t=PTbFqSs&sid=djqbSlnPkxk0715_AAAC HTTP/1.1" 200 -
2025-06-12 20:46:04,921 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:46:04] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:46:35,222 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:46:35] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:47:04,920 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:47:04] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:47:35,231 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:47:35] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:48:04,918 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:04] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:48:35,219 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:35] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:48:36,975 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:36] "GET / HTTP/1.1" 200 -
2025-06-12 20:48:36,983 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:36] "GET /socket.io/?EIO=4&transport=websocket&sid=djqbSlnPkxk0715_AAAC HTTP/1.1" 200 -
2025-06-12 20:48:37,317 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:37] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:48:37,317 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:37] "GET /socket.io/?EIO=4&transport=polling&t=PTbGWq4 HTTP/1.1" 200 -
2025-06-12 20:48:37,567 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:37] "POST /socket.io/?EIO=4&transport=polling&t=PTbGWvD&sid=DPAjKg_X-f7_tA9QAAAE HTTP/1.1" 200 -
2025-06-12 20:48:37,642 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:37] "GET /socket.io/?EIO=4&transport=polling&t=PTbGWvE&sid=DPAjKg_X-f7_tA9QAAAE HTTP/1.1" 200 -
2025-06-12 20:48:37,654 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:37] "POST /socket.io/?EIO=4&transport=polling&t=PTbGW-F&sid=DPAjKg_X-f7_tA9QAAAE HTTP/1.1" 200 -
2025-06-12 20:48:37,952 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:37] "GET /socket.io/?EIO=4&transport=polling&t=PTbGW-E&sid=DPAjKg_X-f7_tA9QAAAE HTTP/1.1" 200 -
2025-06-12 20:48:39,544 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:39] "GET / HTTP/1.1" 200 -
2025-06-12 20:48:40,423 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:40] "GET /socket.io/?EIO=4&transport=websocket&sid=DPAjKg_X-f7_tA9QAAAE HTTP/1.1" 200 -
2025-06-12 20:48:40,439 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:40] "GET /socket.io/?EIO=4&transport=polling&t=PTbGXfp HTTP/1.1" 200 -
2025-06-12 20:48:40,751 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:40] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:48:40,787 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:40] "POST /socket.io/?EIO=4&transport=polling&t=PTbGXg2&sid=T3z58QwcMkuXKs4xAAAG HTTP/1.1" 200 -
2025-06-12 20:48:40,787 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:40] "GET /socket.io/?EIO=4&transport=polling&t=PTbGXg2.0&sid=T3z58QwcMkuXKs4xAAAG HTTP/1.1" 200 -
2025-06-12 20:48:41,112 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:41] "GET /socket.io/?EIO=4&transport=polling&t=PTbGXlP&sid=T3z58QwcMkuXKs4xAAAG HTTP/1.1" 200 -
2025-06-12 20:48:41,117 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:41] "POST /socket.io/?EIO=4&transport=polling&t=PTbGXlQ&sid=T3z58QwcMkuXKs4xAAAG HTTP/1.1" 200 -
2025-06-12 20:48:57,152 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\stream\\enhanced_camera_manager.py', reloading
2025-06-12 20:48:57,153 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\stream\\enhanced_camera_manager.py', reloading
2025-06-12 20:48:57,154 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\stream\\enhanced_camera_manager.py', reloading
2025-06-12 20:48:57,822 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:48:59,286 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:48:59,293 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:48:59,296 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:48:59,595 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:59] "GET /socket.io/?EIO=4&transport=polling&t=PTbGcGD HTTP/1.1" 200 -
2025-06-12 20:48:59,865 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:59] "POST /socket.io/?EIO=4&transport=polling&t=PTbGcLI&sid=F6vFxiSdoLvy69sbAAAA HTTP/1.1" 200 -
2025-06-12 20:48:59,908 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:59] "GET /socket.io/?EIO=4&transport=polling&t=PTbGcLI.0&sid=F6vFxiSdoLvy69sbAAAA HTTP/1.1" 200 -
2025-06-12 20:48:59,918 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:48:59] "GET /socket.io/?EIO=4&transport=polling&t=PTbGcQB&sid=F6vFxiSdoLvy69sbAAAA HTTP/1.1" 200 -
2025-06-12 20:49:08,098 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:49:08,100 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:49:08,100 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:49:08,764 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:49:09,995 - stream.enhanced_camera_manager - INFO - تم إنشاء مدير الكاميرات المحسن
2025-06-12 20:49:10,005 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:49:10,013 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:49:10,015 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:49:10,042 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:49:10] "GET /socket.io/?EIO=4&transport=polling&t=PTbGeo1 HTTP/1.1" 200 -
2025-06-12 20:49:10,218 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:49:10] "POST /socket.io/?EIO=4&transport=polling&t=PTbGeuX&sid=oVQz-1ucrffZTWGeAAAA HTTP/1.1" 200 -
2025-06-12 20:49:10,354 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:49:10] "GET /socket.io/?EIO=4&transport=polling&t=PTbGeuX.0&sid=oVQz-1ucrffZTWGeAAAA HTTP/1.1" 200 -
2025-06-12 20:49:10,364 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:49:10] "GET /socket.io/?EIO=4&transport=polling&t=PTbGezO&sid=oVQz-1ucrffZTWGeAAAA HTTP/1.1" 200 -
2025-06-12 20:49:10,764 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:49:10] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:49:40,441 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:49:40] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:50:03,452 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:03] "GET /settings HTTP/1.1" 200 -
2025-06-12 20:50:03,479 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:03] "GET /socket.io/?EIO=4&transport=websocket&sid=oVQz-1ucrffZTWGeAAAA HTTP/1.1" 200 -
2025-06-12 20:50:05,850 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:05] "GET /socket.io/?EIO=4&transport=polling&t=PTbGsWI HTTP/1.1" 200 -
2025-06-12 20:50:06,203 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:06] "POST /socket.io/?EIO=4&transport=polling&t=PTbGsWd&sid=d_2stWuLKYumaB2MAAAC HTTP/1.1" 200 -
2025-06-12 20:50:06,203 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:06] "GET /socket.io/?EIO=4&transport=polling&t=PTbGsWd.0&sid=d_2stWuLKYumaB2MAAAC HTTP/1.1" 200 -
2025-06-12 20:50:06,464 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:06] "GET /socket.io/?EIO=4&transport=polling&t=PTbGsc2&sid=d_2stWuLKYumaB2MAAAC HTTP/1.1" 200 -
2025-06-12 20:50:06,531 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:06] "POST /socket.io/?EIO=4&transport=polling&t=PTbGsc3&sid=d_2stWuLKYumaB2MAAAC HTTP/1.1" 200 -
2025-06-12 20:50:08,052 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:08] "GET /events HTTP/1.1" 200 -
2025-06-12 20:50:08,085 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:08] "GET /socket.io/?EIO=4&transport=websocket&sid=d_2stWuLKYumaB2MAAAC HTTP/1.1" 200 -
2025-06-12 20:50:08,402 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:08] "[33mGET /api/events/stats HTTP/1.1[0m" 404 -
2025-06-12 20:50:09,291 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:09] "GET /recordings HTTP/1.1" 200 -
2025-06-12 20:50:10,639 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:10] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:50:10,894 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:10] "GET /socket.io/?EIO=4&transport=polling&t=PTbGtha HTTP/1.1" 200 -
2025-06-12 20:50:10,978 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:10] "POST /socket.io/?EIO=4&transport=polling&t=PTbGtlK&sid=stP2QiMvwm4-Xx3rAAAE HTTP/1.1" 200 -
2025-06-12 20:50:11,209 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:11] "GET /socket.io/?EIO=4&transport=polling&t=PTbGtlL&sid=stP2QiMvwm4-Xx3rAAAE HTTP/1.1" 200 -
2025-06-12 20:50:11,221 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:11] "POST /socket.io/?EIO=4&transport=polling&t=PTbGtqE.0&sid=stP2QiMvwm4-Xx3rAAAE HTTP/1.1" 200 -
2025-06-12 20:50:11,522 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:11] "GET /socket.io/?EIO=4&transport=polling&t=PTbGtqE&sid=stP2QiMvwm4-Xx3rAAAE HTTP/1.1" 200 -
2025-06-12 20:50:15,480 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:15] "GET / HTTP/1.1" 200 -
2025-06-12 20:50:15,507 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:15] "GET /socket.io/?EIO=4&transport=websocket&sid=stP2QiMvwm4-Xx3rAAAE HTTP/1.1" 200 -
2025-06-12 20:50:15,722 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:15] "GET /socket.io/?EIO=4&transport=polling&t=PTbGutB HTTP/1.1" 200 -
2025-06-12 20:50:15,816 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:15] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:50:15,819 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:15] "POST /socket.io/?EIO=4&transport=polling&t=PTbGuwn&sid=HR0LlJDGRGHR8S1UAAAG HTTP/1.1" 200 -
2025-06-12 20:50:16,035 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:16] "GET /socket.io/?EIO=4&transport=polling&t=PTbGuwn.0&sid=HR0LlJDGRGHR8S1UAAAG HTTP/1.1" 200 -
2025-06-12 20:50:16,048 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:16] "POST /socket.io/?EIO=4&transport=polling&t=PTbGu_f.0&sid=HR0LlJDGRGHR8S1UAAAG HTTP/1.1" 200 -
2025-06-12 20:50:16,346 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:16] "GET /socket.io/?EIO=4&transport=polling&t=PTbGu_f&sid=HR0LlJDGRGHR8S1UAAAG HTTP/1.1" 200 -
2025-06-12 20:50:18,321 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:18] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:50:18,353 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:18] "GET /socket.io/?EIO=4&transport=websocket&sid=HR0LlJDGRGHR8S1UAAAG HTTP/1.1" 200 -
2025-06-12 20:50:18,584 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:18] "GET /socket.io/?EIO=4&transport=polling&t=PTbGvZa HTTP/1.1" 200 -
2025-06-12 20:50:18,666 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:18] "POST /socket.io/?EIO=4&transport=polling&t=PTbGvdW&sid=AE2hU5NM6DQStG0aAAAI HTTP/1.1" 200 -
2025-06-12 20:50:18,902 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:18] "GET /socket.io/?EIO=4&transport=polling&t=PTbGvdW.0&sid=AE2hU5NM6DQStG0aAAAI HTTP/1.1" 200 -
2025-06-12 20:50:18,916 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:18] "POST /socket.io/?EIO=4&transport=polling&t=PTbGviS.0&sid=AE2hU5NM6DQStG0aAAAI HTTP/1.1" 200 -
2025-06-12 20:50:19,214 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:50:19] "GET /socket.io/?EIO=4&transport=polling&t=PTbGviS&sid=AE2hU5NM6DQStG0aAAAI HTTP/1.1" 200 -
2025-06-12 20:51:03,200 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:51:03,437 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_dtype_like.py', reloading
2025-06-12 20:51:03,442 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_array_like.py', reloading
2025-06-12 20:51:04,286 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:04] "POST /api/cameras/test HTTP/1.1" 200 -
2025-06-12 20:51:05,614 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:51:06,821 - stream.enhanced_camera_manager - INFO - تم إنشاء مدير الكاميرات المحسن
2025-06-12 20:51:06,836 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 1
2025-06-12 20:51:06,836 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 2
2025-06-12 20:51:06,836 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 3
2025-06-12 20:51:06,836 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 4
2025-06-12 20:51:06,836 - __main__ - INFO - تم إضافة كاميرات تجريبية
2025-06-12 20:51:06,837 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:51:06,842 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:51:06,845 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:51:06,884 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:06] "GET /socket.io/?EIO=4&transport=polling&t=PTbH5Ex HTTP/1.1" 200 -
2025-06-12 20:51:06,901 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:06] "POST /socket.io/?EIO=4&transport=polling&t=PTbH5QC&sid=96OiXq1WlOG4HSGEAAAA HTTP/1.1" 200 -
2025-06-12 20:51:07,209 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:07] "GET /socket.io/?EIO=4&transport=polling&t=PTbH5QC.0&sid=96OiXq1WlOG4HSGEAAAA HTTP/1.1" 200 -
2025-06-12 20:51:07,536 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:07] "GET /socket.io/?EIO=4&transport=polling&t=PTbH5VF&sid=96OiXq1WlOG4HSGEAAAA HTTP/1.1" 200 -
2025-06-12 20:51:09,990 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:09] "POST /api/cameras HTTP/1.1" 200 -
2025-06-12 20:51:10,337 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:10] "GET /cameras HTTP/1.1" 200 -
2025-06-12 20:51:10,361 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:10] "GET /socket.io/?EIO=4&transport=websocket&sid=96OiXq1WlOG4HSGEAAAA HTTP/1.1" 200 -
2025-06-12 20:51:10,586 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:10] "GET /socket.io/?EIO=4&transport=polling&t=PTbH6Gc HTTP/1.1" 200 -
2025-06-12 20:51:10,698 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:10] "POST /socket.io/?EIO=4&transport=polling&t=PTbH6K0&sid=1fap-i-Dl577e1EXAAAC HTTP/1.1" 200 -
2025-06-12 20:51:10,898 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:10] "GET /socket.io/?EIO=4&transport=polling&t=PTbH6K0.0&sid=1fap-i-Dl577e1EXAAAC HTTP/1.1" 200 -
2025-06-12 20:51:10,911 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:10] "POST /socket.io/?EIO=4&transport=polling&t=PTbH6Ov&sid=1fap-i-Dl577e1EXAAAC HTTP/1.1" 200 -
2025-06-12 20:51:11,214 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:11] "GET /socket.io/?EIO=4&transport=polling&t=PTbH6Ou&sid=1fap-i-Dl577e1EXAAAC HTTP/1.1" 200 -
2025-06-12 20:51:13,371 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:13] "GET / HTTP/1.1" 200 -
2025-06-12 20:51:13,406 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:13] "GET /socket.io/?EIO=4&transport=websocket&sid=1fap-i-Dl577e1EXAAAC HTTP/1.1" 200 -
2025-06-12 20:51:13,627 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbH6_s HTTP/1.1" 200 -
2025-06-12 20:51:13,721 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:13] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:51:13,725 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:13] "POST /socket.io/?EIO=4&transport=polling&t=PTbH73X&sid=JPMxvS9JLDAnQO6zAAAE HTTP/1.1" 200 -
2025-06-12 20:51:13,941 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:13] "GET /socket.io/?EIO=4&transport=polling&t=PTbH73Y&sid=JPMxvS9JLDAnQO6zAAAE HTTP/1.1" 200 -
2025-06-12 20:51:13,954 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:13] "POST /socket.io/?EIO=4&transport=polling&t=PTbH78R.0&sid=JPMxvS9JLDAnQO6zAAAE HTTP/1.1" 200 -
2025-06-12 20:51:14,259 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:14] "GET /socket.io/?EIO=4&transport=polling&t=PTbH78R&sid=JPMxvS9JLDAnQO6zAAAE HTTP/1.1" 200 -
2025-06-12 20:51:33,238 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:51:33,240 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:51:33,241 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\app.py', reloading
2025-06-12 20:51:33,743 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:51:34,987 - stream.enhanced_camera_manager - INFO - تم إنشاء مدير الكاميرات المحسن
2025-06-12 20:51:34,998 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 1
2025-06-12 20:51:34,998 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 2
2025-06-12 20:51:34,998 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 3
2025-06-12 20:51:34,998 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 4
2025-06-12 20:51:34,999 - __main__ - INFO - تم إضافة كاميرات تجريبية
2025-06-12 20:51:34,999 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:51:35,004 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:51:35,006 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:51:35,122 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:35] "GET /socket.io/?EIO=4&transport=polling&t=PTbHCEH HTTP/1.1" 200 -
2025-06-12 20:51:35,377 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:35] "POST /socket.io/?EIO=4&transport=polling&t=PTbHCJO&sid=D49iWchIxMBJgDm3AAAA HTTP/1.1" 200 -
2025-06-12 20:51:35,437 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:35] "GET /socket.io/?EIO=4&transport=polling&t=PTbHCJP&sid=D49iWchIxMBJgDm3AAAA HTTP/1.1" 200 -
2025-06-12 20:51:35,445 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:35] "GET /socket.io/?EIO=4&transport=polling&t=PTbHCOI&sid=D49iWchIxMBJgDm3AAAA HTTP/1.1" 200 -
2025-06-12 20:51:43,718 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:51:43] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:52:13,417 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:52:13] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:52:43,714 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:52:43] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:53:13,411 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:53:13] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:53:43,705 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:53:43] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:54:13,411 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:54:13] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:54:43,719 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:54:43] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:55:13,409 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:55:13] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:55:43,724 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:55:43] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:55:55,621 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\run_enhanced.py', reloading
2025-06-12 20:55:55,642 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\run_enhanced.py', reloading
2025-06-12 20:55:55,642 - werkzeug - INFO -  * Detected change in 'e:\\Visual Studio Code\\063\\run_enhanced.py', reloading
2025-06-12 20:55:56,407 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-12 20:55:57,675 - stream.enhanced_camera_manager - INFO - تم إنشاء مدير الكاميرات المحسن
2025-06-12 20:55:57,687 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 1
2025-06-12 20:55:57,687 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 2
2025-06-12 20:55:57,687 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 3
2025-06-12 20:55:57,688 - stream.enhanced_camera_manager - INFO - تم إضافة الكاميرا 4
2025-06-12 20:55:57,688 - __main__ - INFO - تم إضافة كاميرات تجريبية
2025-06-12 20:55:57,688 - __main__ - INFO - 🚀 بدء تشغيل نظام المراقبة الذكي على 0.0.0.0:5000
2025-06-12 20:55:57,693 - werkzeug - WARNING -  * Debugger is active!
2025-06-12 20:55:57,695 - werkzeug - INFO -  * Debugger PIN: 942-643-529
2025-06-12 20:55:57,726 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:55:57] "GET /socket.io/?EIO=4&transport=polling&t=PTbICKu HTTP/1.1" 200 -
2025-06-12 20:55:57,957 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:55:57] "POST /socket.io/?EIO=4&transport=polling&t=PTbICQc&sid=JlxtRunFyhy-XZG1AAAA HTTP/1.1" 200 -
2025-06-12 20:55:58,045 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:55:58] "GET /socket.io/?EIO=4&transport=polling&t=PTbICQd&sid=JlxtRunFyhy-XZG1AAAA HTTP/1.1" 200 -
2025-06-12 20:55:58,054 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:55:58] "GET /socket.io/?EIO=4&transport=polling&t=PTbICVZ&sid=JlxtRunFyhy-XZG1AAAA HTTP/1.1" 200 -
2025-06-12 20:56:13,720 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:13] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:56:21,617 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:21] "GET / HTTP/1.1" 200 -
2025-06-12 20:56:21,639 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:21] "GET /socket.io/?EIO=4&transport=websocket&sid=JlxtRunFyhy-XZG1AAAA HTTP/1.1" 200 -
2025-06-12 20:56:21,965 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:21] "[33mGET /api/stats HTTP/1.1[0m" 404 -
2025-06-12 20:56:21,973 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:21] "GET /socket.io/?EIO=4&transport=polling&t=PTbIIGO HTTP/1.1" 200 -
2025-06-12 20:56:22,002 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:22] "GET /api/cameras/4/stream HTTP/1.1" 200 -
2025-06-12 20:56:22,002 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:22] "GET /api/cameras/2/stream HTTP/1.1" 200 -
2025-06-12 20:56:22,002 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:22] "GET /api/cameras/1/stream HTTP/1.1" 200 -
2025-06-12 20:56:22,002 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:22] "GET /api/cameras/3/stream HTTP/1.1" 200 -
2025-06-12 20:56:22,296 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:22] "POST /socket.io/?EIO=4&transport=polling&t=PTbIILT&sid=1r51MpvQFSVMdJClAAAC HTTP/1.1" 200 -
2025-06-12 20:56:22,296 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:22] "GET /socket.io/?EIO=4&transport=polling&t=PTbIILT.0&sid=1r51MpvQFSVMdJClAAAC HTTP/1.1" 200 -
2025-06-12 20:56:22,607 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:22] "GET /socket.io/?EIO=4&transport=polling&t=PTbIIQU&sid=1r51MpvQFSVMdJClAAAC HTTP/1.1" 200 -
2025-06-12 20:56:22,613 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:22] "GET /api/cameras/5/stream HTTP/1.1" 200 -
2025-06-12 20:56:23,982 - stream.enhanced_camera_manager - INFO - محاولة الاتصال بالكاميرا 1: rtsp://admin:admin@admin:admin@192.168.1.100:554/stream1
2025-06-12 20:56:23,982 - stream.enhanced_camera_manager - INFO - تم بدء بث الكاميرا 1
2025-06-12 20:56:23,983 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:23] "POST /api/cameras/1/start HTTP/1.1" 200 -
2025-06-12 20:56:24,000 - stream.enhanced_camera_manager - INFO - تم إنشاء مدير الكاميرات المحسن
2025-06-12 20:56:24,297 - stream.enhanced_camera_manager - INFO - محاولة الاتصال بالكاميرا 2: rtsp://admin:admin@admin:admin@192.168.1.101:554/stream1
2025-06-12 20:56:24,297 - stream.enhanced_camera_manager - INFO - تم بدء بث الكاميرا 2
2025-06-12 20:56:24,298 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:24] "POST /api/cameras/2/start HTTP/1.1" 200 -
2025-06-12 20:56:24,624 - stream.enhanced_camera_manager - INFO - محاولة الاتصال بالكاميرا 3: rtsp://admin:admin@admin:admin@192.168.1.102:554/stream1
2025-06-12 20:56:24,624 - stream.enhanced_camera_manager - INFO - تم بدء بث الكاميرا 3
2025-06-12 20:56:24,625 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:24] "POST /api/cameras/3/start HTTP/1.1" 200 -
2025-06-12 20:56:24,937 - stream.enhanced_camera_manager - INFO - محاولة الاتصال بالكاميرا 4: rtsp://admin:admin@admin:admin@192.168.1.103:554/stream1
2025-06-12 20:56:24,938 - stream.enhanced_camera_manager - INFO - تم بدء بث الكاميرا 4
2025-06-12 20:56:24,938 - werkzeug - INFO - 127.0.0.1 - - [12/Jun/2025 20:56:24] "POST /api/cameras/4/start HTTP/1.1" 200 -
