#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لنظام المراقبة الذكي
Quick Test for Smart Surveillance System
"""

import os
import sys
import json
import importlib.util

def test_python_version():
    """اختبار إصدار Python"""
    print("🐍 اختبار إصدار Python...")
    if sys.version_info >= (3, 8):
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} - مدعوم")
        return True
    else:
        print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} - غير مدعوم (يتطلب 3.8+)")
        return False

def test_required_modules():
    """اختبار المكتبات الأساسية"""
    print("\n📦 اختبار المكتبات الأساسية...")
    
    basic_modules = [
        ('json', 'مكتبة JSON'),
        ('os', 'مكتبة نظام التشغيل'),
        ('threading', 'مكتبة الخيوط'),
        ('datetime', 'مكتبة التاريخ والوقت'),
        ('logging', 'مكتبة السجلات')
    ]
    
    all_passed = True
    for module_name, description in basic_modules:
        try:
            __import__(module_name)
            print(f"✅ {description}")
        except ImportError:
            print(f"❌ {description} - غير متوفر")
            all_passed = False
    
    return all_passed

def test_optional_modules():
    """اختبار المكتبات الاختيارية"""
    print("\n🔧 اختبار المكتبات الاختيارية...")
    
    optional_modules = [
        ('flask', 'Flask Framework'),
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('PIL', 'Pillow'),
        ('sqlite3', 'SQLite')
    ]
    
    available_modules = []
    for module_name, description in optional_modules:
        try:
            __import__(module_name)
            print(f"✅ {description}")
            available_modules.append(module_name)
        except ImportError:
            print(f"⚠️  {description} - غير مثبت (اختياري)")
    
    return available_modules

def test_config_file():
    """اختبار ملف الإعدادات"""
    print("\n⚙️ اختبار ملف الإعدادات...")
    
    if not os.path.exists('config.json'):
        print("❌ ملف config.json غير موجود")
        return False
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_sections = ['system', 'server', 'database', 'cameras']
        missing_sections = []
        
        for section in required_sections:
            if section in config:
                print(f"✅ قسم {section}")
            else:
                print(f"❌ قسم {section} مفقود")
                missing_sections.append(section)
        
        if missing_sections:
            print(f"⚠️  أقسام مفقودة: {', '.join(missing_sections)}")
            return False
        
        print("✅ ملف الإعدادات صحيح")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ خطأ في تحليل ملف الإعدادات: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الإعدادات: {str(e)}")
        return False

def test_directories():
    """اختبار المجلدات المطلوبة"""
    print("\n📁 اختبار المجلدات...")
    
    required_dirs = [
        'templates',
        'static', 
        'stream',
        'ai_modules',
        'recordings',
        'logs',
        'known_faces',
        'database',
        'utils'
    ]
    
    all_exist = True
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✅ مجلد {directory}")
        else:
            print(f"❌ مجلد {directory} غير موجود")
            all_exist = False
    
    return all_exist

def test_template_files():
    """اختبار ملفات القوالب"""
    print("\n📄 اختبار ملفات القوالب...")
    
    required_templates = [
        'base.html',
        'login.html',
        'index.html',
        'cameras.html'
    ]
    
    all_exist = True
    for template in required_templates:
        template_path = os.path.join('templates', template)
        if os.path.exists(template_path):
            print(f"✅ قالب {template}")
        else:
            print(f"❌ قالب {template} غير موجود")
            all_exist = False
    
    return all_exist

def test_app_import():
    """اختبار استيراد التطبيق الرئيسي"""
    print("\n🚀 اختبار استيراد التطبيق...")
    
    try:
        # محاولة استيراد الوحدات الأساسية
        spec = importlib.util.spec_from_file_location("app", "app.py")
        if spec is None:
            print("❌ لا يمكن العثور على app.py")
            return False
        
        print("✅ تم العثور على app.py")
        
        # اختبار استيراد وحدة البث
        stream_init = os.path.join('stream', '__init__.py')
        if os.path.exists(stream_init):
            print("✅ وحدة البث متوفرة")
        else:
            print("❌ وحدة البث غير متوفرة")
            return False
        
        # اختبار استيراد وحدة الذكاء الاصطناعي
        ai_init = os.path.join('ai_modules', '__init__.py')
        if os.path.exists(ai_init):
            print("✅ وحدة الذكاء الاصطناعي متوفرة")
        else:
            print("❌ وحدة الذكاء الاصطناعي غير متوفرة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد التطبيق: {str(e)}")
        return False

def create_missing_files():
    """إنشاء الملفات المفقودة"""
    print("\n🔧 إنشاء الملفات المفقودة...")
    
    # إنشاء ملف __init__.py في utils إذا لم يكن موجوداً
    utils_init = os.path.join('utils', '__init__.py')
    if not os.path.exists(utils_init):
        with open(utils_init, 'w', encoding='utf-8') as f:
            f.write('# أدوات مساعدة\n# Utility functions\n')
        print("✅ تم إنشاء utils/__init__.py")
    
    # إنشاء ملف .gitkeep في المجلدات الفارغة
    empty_dirs = ['recordings', 'logs', 'known_faces', 'database']
    for directory in empty_dirs:
        gitkeep_path = os.path.join(directory, '.gitkeep')
        if not os.path.exists(gitkeep_path):
            with open(gitkeep_path, 'w') as f:
                f.write('')
            print(f"✅ تم إنشاء {directory}/.gitkeep")

def print_summary(results):
    """طباعة ملخص النتائج"""
    print("\n" + "="*60)
    print("📊 ملخص نتائج الاختبار")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"إجمالي الاختبارات: {total_tests}")
    print(f"نجح: {passed_tests}")
    print(f"فشل: {total_tests - passed_tests}")
    print(f"معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\nتفاصيل النتائج:")
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للتشغيل")
        print("\nلتشغيل النظام:")
        print("  python run.py")
        print("  أو")
        print("  start.bat")
    else:
        print("\n⚠️  بعض الاختبارات فشلت. يرجى إصلاح المشاكل قبل التشغيل")
        print("\nلتثبيت المكتبات المطلوبة:")
        print("  pip install -r requirements.txt")

def main():
    """الوظيفة الرئيسية"""
    print("🎥 نظام المراقبة الذكي - اختبار النظام")
    print("Smart Surveillance System - System Test")
    print("="*60)
    
    # تشغيل الاختبارات
    results = {}
    
    results['إصدار Python'] = test_python_version()
    results['المكتبات الأساسية'] = test_required_modules()
    results['ملف الإعدادات'] = test_config_file()
    results['المجلدات'] = test_directories()
    results['ملفات القوالب'] = test_template_files()
    results['استيراد التطبيق'] = test_app_import()
    
    # اختبار المكتبات الاختيارية (لا يؤثر على النتيجة النهائية)
    available_modules = test_optional_modules()
    
    # إنشاء الملفات المفقودة
    create_missing_files()
    
    # طباعة الملخص
    print_summary(results)
    
    # معلومات إضافية
    if available_modules:
        print(f"\n📦 المكتبات المتوفرة: {', '.join(available_modules)}")
    
    print(f"\n📁 مجلد العمل: {os.getcwd()}")
    print(f"🐍 Python: {sys.executable}")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
