{% extends "base.html" %}

{% block title %}الرئيسية - نظام المراقبة الذكي{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="activeCameras">{{ cameras|length }}</div>
                        <div class="stats-label">كاميرات نشطة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-video fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="onlineStatus">0</div>
                        <div class="stats-label">متصلة الآن</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-wifi fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="recordingCount">0</div>
                        <div class="stats-label">تسجيلات اليوم</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-record-vinyl fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="alertsCount">{{ events|length }}</div>
                        <div class="stats-label">تنبيهات جديدة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أزرار التحكم السريع -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sliders-h me-2"></i>
                        التحكم السريع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-success" onclick="startAllRecording()">
                            <i class="fas fa-play me-1"></i>
                            بدء التسجيل للكل
                        </button>
                        <button type="button" class="btn btn-danger" onclick="stopAllRecording()">
                            <i class="fas fa-stop me-1"></i>
                            إيقاف التسجيل
                        </button>
                    </div>
                    
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-primary" onclick="refreshAllStreams()">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث البث
                        </button>
                        <button type="button" class="btn btn-warning" onclick="toggleMotionDetection()">
                            <i class="fas fa-running me-1"></i>
                            كشف الحركة
                        </button>
                    </div>
                    
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-info" onclick="showGridOptions()">
                            <i class="fas fa-th me-1"></i>
                            تخطيط الشبكة
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="toggleFullscreen()">
                            <i class="fas fa-expand me-1"></i>
                            ملء الشاشة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- شبكة الكاميرات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-video me-2"></i>
                        البث المباشر
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-light" onclick="setGridLayout('1x1')">1×1</button>
                        <button class="btn btn-outline-light active" onclick="setGridLayout('2x2')">2×2</button>
                        <button class="btn btn-outline-light" onclick="setGridLayout('3x3')">3×3</button>
                        <button class="btn btn-outline-light" onclick="setGridLayout('4x4')">4×4</button>
                    </div>
                </div>
                <div class="card-body p-2">
                    <div id="cameraGrid" class="camera-grid grid-2x2">
                        {% if cameras %}
                            {% for camera in cameras %}
                            <div class="camera-card" data-camera-id="{{ camera.id }}">
                                <div class="camera-stream" id="stream-{{ camera.id }}">
                                    <div class="loading-spinner"></div>
                                    <div class="stream-placeholder">
                                        <i class="fas fa-video fa-3x mb-2"></i>
                                        <p>جاري تحميل البث...</p>
                                    </div>
                                </div>
                                <div class="camera-info">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">{{ camera.name }}</h6>
                                            <small class="text-muted">{{ camera.type.upper() }}</small>
                                        </div>
                                        <div class="camera-controls">
                                            <button class="btn btn-sm btn-outline-light me-1" onclick="toggleRecording({{ camera.id }})">
                                                <i class="fas fa-record-vinyl"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-light me-1" onclick="takeSnapshot({{ camera.id }})">
                                                <i class="fas fa-camera"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-light" onclick="showFullscreen({{ camera.id }})">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <span class="badge bg-success status-indicator" id="status-{{ camera.id }}">
                                            <i class="fas fa-circle me-1"></i>
                                            متصل
                                        </span>
                                        <span class="badge bg-info ms-1">
                                            <i class="fas fa-clock me-1"></i>
                                            <span id="uptime-{{ camera.id }}">00:00:00</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="col-12 text-center py-5">
                                <i class="fas fa-video-slash fa-4x text-muted mb-3"></i>
                                <h4 class="text-muted">لا توجد كاميرات مضافة</h4>
                                <p class="text-muted">قم بإضافة كاميرات جديدة من قسم إدارة الكاميرات</p>
                                <a href="{{ url_for('cameras') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة كاميرا
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الأحداث الأخيرة -->
    {% if events %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        الأحداث الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>الكاميرا</th>
                                    <th>نوع الحدث</th>
                                    <th>الوصف</th>
                                    <th>الثقة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for event in events[:5] %}
                                <tr>
                                    <td>{{ event.created_at.strftime('%H:%M:%S') }}</td>
                                    <td>كاميرا {{ event.camera_id }}</td>
                                    <td>
                                        <span class="badge bg-warning">{{ event.event_type }}</span>
                                    </td>
                                    <td>{{ event.description or 'لا يوجد وصف' }}</td>
                                    <td>
                                        {% if event.confidence %}
                                            <div class="progress" style="width: 60px; height: 20px;">
                                                <div class="progress-bar bg-success" style="width: {{ (event.confidence * 100)|round }}%">
                                                    {{ (event.confidence * 100)|round }}%
                                                </div>
                                            </div>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('events') }}" class="btn btn-outline-light">
                            عرض جميع الأحداث
                            <i class="fas fa-arrow-left ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- نافذة منبثقة للعرض بملء الشاشة -->
<div class="modal fade" id="fullscreenModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content bg-dark">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white" id="fullscreenTitle">عرض بملء الشاشة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div id="fullscreenStream" class="w-100 h-100 d-flex align-items-center justify-content-center">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.grid-1x1 { grid-template-columns: 1fr; }
.grid-2x2 { grid-template-columns: repeat(2, 1fr); }
.grid-3x3 { grid-template-columns: repeat(3, 1fr); }
.grid-4x4 { grid-template-columns: repeat(4, 1fr); }

.camera-stream {
    position: relative;
    background: #2c3e50;
    aspect-ratio: 16/9;
}

.stream-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #7f8c8d;
}

.camera-controls .btn {
    border-radius: 50%;
    width: 35px;
    height: 35px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.status-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let socket = io();
let currentLayout = '2x2';
let recordingStates = {};

// الاتصال بـ Socket.IO
socket.on('connect', function() {
    console.log('تم الاتصال بالخادم');
    updateConnectionStatus(true);
});

socket.on('disconnect', function() {
    console.log('انقطع الاتصال بالخادم');
    updateConnectionStatus(false);
});

// استقبال البث المباشر
socket.on('video_frame', function(data) {
    const streamElement = document.getElementById('stream-' + data.camera_id);
    if (streamElement) {
        streamElement.innerHTML = `<img src="data:image/jpeg;base64,${data.frame}" class="w-100 h-100" style="object-fit: cover;">`;
        updateCameraStatus(data.camera_id, 'online');
    }
});

// استقبال حالة الكاميرات
socket.on('camera_status', function(data) {
    updateCameraStatus(data.camera_id, data.status);
});

// وظائف التحكم
function startAllRecording() {
    socket.emit('start_all_recording');
    showNotification('تم بدء التسجيل لجميع الكاميرات', 'success');
}

function stopAllRecording() {
    socket.emit('stop_all_recording');
    showNotification('تم إيقاف التسجيل لجميع الكاميرات', 'info');
}

function refreshAllStreams() {
    socket.emit('refresh_streams');
    showNotification('جاري تحديث البث...', 'info');
}

function toggleMotionDetection() {
    socket.emit('toggle_motion_detection');
    showNotification('تم تبديل حالة كشف الحركة', 'warning');
}

function setGridLayout(layout) {
    currentLayout = layout;
    const grid = document.getElementById('cameraGrid');
    grid.className = `camera-grid grid-${layout}`;
    
    // تحديث الأزرار النشطة
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

function toggleRecording(cameraId) {
    const isRecording = recordingStates[cameraId] || false;
    if (isRecording) {
        socket.emit('stop_recording', {camera_id: cameraId});
    } else {
        socket.emit('start_recording', {camera_id: cameraId});
    }
    recordingStates[cameraId] = !isRecording;
}

function takeSnapshot(cameraId) {
    socket.emit('take_snapshot', {camera_id: cameraId});
    showNotification('تم التقاط لقطة شاشة', 'success');
}

function showFullscreen(cameraId) {
    const modal = new bootstrap.Modal(document.getElementById('fullscreenModal'));
    document.getElementById('fullscreenTitle').textContent = `كاميرا ${cameraId} - عرض بملء الشاشة`;
    modal.show();
    
    // طلب البث بدقة عالية
    socket.emit('request_hd_stream', {camera_id: cameraId});
}

function updateCameraStatus(cameraId, status) {
    const statusElement = document.getElementById('status-' + cameraId);
    if (statusElement) {
        statusElement.className = `badge ${status === 'online' ? 'bg-success' : 'bg-danger'} status-indicator`;
        statusElement.innerHTML = `<i class="fas fa-circle me-1"></i>${status === 'online' ? 'متصل' : 'غير متصل'}`;
    }
}

function updateConnectionStatus(connected) {
    const statusElements = document.querySelectorAll('.status-indicator');
    statusElements.forEach(el => {
        if (!connected) {
            el.className = 'badge bg-secondary status-indicator';
            el.innerHTML = '<i class="fas fa-circle me-1"></i>منقطع';
        }
    });
}

function showNotification(message, type = 'info') {
    // إنشاء تنبيه مؤقت
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// تحديث الوقت المباشر
function updateUptime() {
    const uptimeElements = document.querySelectorAll('[id^="uptime-"]');
    uptimeElements.forEach(el => {
        // محاكاة وقت التشغيل
        const currentTime = new Date();
        const hours = String(currentTime.getHours()).padStart(2, '0');
        const minutes = String(currentTime.getMinutes()).padStart(2, '0');
        const seconds = String(currentTime.getSeconds()).padStart(2, '0');
        el.textContent = `${hours}:${minutes}:${seconds}`;
    });
}

// تشغيل التحديثات الدورية
setInterval(updateUptime, 1000);

// تحميل البث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // طلب بدء البث لجميع الكاميرات
    socket.emit('start_streaming');
    
    // تحديث الإحصائيات
    updateStats();
});

function updateStats() {
    // تحديث الإحصائيات من الخادم
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('onlineStatus').textContent = data.online_cameras || 0;
            document.getElementById('recordingCount').textContent = data.recordings_today || 0;
        })
        .catch(error => console.error('خطأ في تحديث الإحصائيات:', error));
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(updateStats, 30000);
</script>
{% endblock %}
