{% extends "base.html" %}

{% block title %}الأحداث - نظام المراقبة الذكي{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-bell me-2"></i>
                    مراقبة الأحداث
                </h2>
                <div class="btn-group" role="group">
                    <button class="btn btn-success" onclick="markAllAsRead()">
                        <i class="fas fa-check-double me-2"></i>
                        تحديد الكل كمقروء
                    </button>
                    <button class="btn btn-danger" onclick="clearAllEvents()">
                        <i class="fas fa-trash me-2"></i>
                        مسح جميع الأحداث
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ events|length }}</div>
                        <div class="stats-label">إجمالي الأحداث</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-bell fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="motionEvents">0</div>
                        <div class="stats-label">كشف حركة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-running fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="faceEvents">0</div>
                        <div class="stats-label">تعرف على وجه</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="alertEvents">0</div>
                        <div class="stats-label">تنبيهات هامة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أدوات التحكم والفلترة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchEvents" 
                                       placeholder="البحث في الأحداث...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="filterEventType">
                                <option value="">جميع الأنواع</option>
                                <option value="motion">كشف حركة</option>
                                <option value="face">تعرف على وجه</option>
                                <option value="object">كشف كائن</option>
                                <option value="alert">تنبيه</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="filterCamera">
                                <option value="">جميع الكاميرات</option>
                                <!-- ستتم إضافة الكاميرات هنا -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="filterDate">
                                <option value="">جميع التواريخ</option>
                                <option value="today">اليوم</option>
                                <option value="yesterday">أمس</option>
                                <option value="week">هذا الأسبوع</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="refreshEvents()">
                                <i class="fas fa-sync-alt me-1"></i>
                                تحديث
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- قائمة الأحداث -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        سجل الأحداث
                    </h5>
                </div>
                <div class="card-body">
                    {% if events %}
                    <div class="events-timeline">
                        {% for event in events %}
                        <div class="event-item" data-event-id="{{ event.id }}">
                            <div class="event-time">
                                {{ event.created_at.strftime('%H:%M') }}
                                <small class="text-muted d-block">
                                    {{ event.created_at.strftime('%Y-%m-%d') }}
                                </small>
                            </div>
                            <div class="event-icon">
                                {% if event.event_type == 'motion' %}
                                    <i class="fas fa-running text-warning"></i>
                                {% elif event.event_type == 'face' %}
                                    <i class="fas fa-user-check text-success"></i>
                                {% elif event.event_type == 'object' %}
                                    <i class="fas fa-cube text-info"></i>
                                {% else %}
                                    <i class="fas fa-bell text-primary"></i>
                                {% endif %}
                            </div>
                            <div class="event-content">
                                <div class="event-header">
                                    <h6 class="event-title">
                                        {% if event.event_type == 'motion' %}
                                            كشف حركة
                                        {% elif event.event_type == 'face' %}
                                            تعرف على وجه
                                        {% elif event.event_type == 'object' %}
                                            كشف كائن
                                        {% else %}
                                            {{ event.event_type }}
                                        {% endif %}
                                    </h6>
                                    <span class="event-camera">كاميرا {{ event.camera_id }}</span>
                                </div>
                                <p class="event-description">
                                    {{ event.description or 'لا يوجد وصف متاح' }}
                                </p>
                                {% if event.confidence %}
                                <div class="event-confidence">
                                    <small class="text-muted">مستوى الثقة:</small>
                                    <div class="progress progress-sm">
                                        <div class="progress-bar bg-success" 
                                             style="width: {{ (event.confidence * 100)|round }}%">
                                            {{ (event.confidence * 100)|round }}%
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                {% if event.image_path %}
                                <div class="event-image mt-2">
                                    <img src="{{ url_for('static', filename='events/' + event.image_path) }}" 
                                         class="img-thumbnail" style="max-width: 200px;" 
                                         onclick="showEventImage('{{ event.image_path }}')">
                                </div>
                                {% endif %}
                                <div class="event-actions mt-2">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewEventDetails({{ event.id }})">
                                        <i class="fas fa-eye me-1"></i>
                                        تفاصيل
                                    </button>
                                    {% if event.image_path %}
                                    <button class="btn btn-sm btn-outline-success" onclick="downloadEventImage({{ event.id }})">
                                        <i class="fas fa-download me-1"></i>
                                        تحميل الصورة
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteEvent({{ event.id }})">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- ترقيم الصفحات -->
                    <nav aria-label="ترقيم الأحداث" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item">
                                <a class="page-link" href="#" aria-label="السابق">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#" aria-label="التالي">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد أحداث</h4>
                        <p class="text-muted">سيتم عرض الأحداث هنا عند حدوثها</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تفاصيل الحدث -->
<div class="modal fade" id="eventDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الحدث
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="eventDetailsContent">
                <!-- ستتم إضافة التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة عرض الصورة -->
<div class="modal fade" id="eventImageModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content bg-dark">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white">صورة الحدث</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0 text-center">
                <img id="eventImageDisplay" src="" class="img-fluid" style="max-height: 80vh;">
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    border-radius: 12px;
    padding: 20px;
    color: white;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.events-timeline {
    position: relative;
}

.event-item {
    display: flex;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border-right: 4px solid #3498db;
    transition: all 0.3s ease;
}

.event-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-5px);
}

.event-time {
    min-width: 80px;
    text-align: center;
    font-weight: 600;
    color: #3498db;
}

.event-icon {
    min-width: 40px;
    text-align: center;
    font-size: 1.2rem;
    margin: 0 15px;
}

.event-content {
    flex: 1;
}

.event-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 8px;
}

.event-title {
    margin: 0;
    color: #ecf0f1;
}

.event-camera {
    font-size: 0.8rem;
    color: #bdc3c7;
    background: rgba(52, 152, 219, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
}

.event-description {
    margin: 8px 0;
    color: #bdc3c7;
    font-size: 0.9rem;
}

.event-confidence {
    margin: 8px 0;
}

.progress-sm {
    height: 8px;
    width: 100px;
}

.event-image img {
    cursor: pointer;
    transition: transform 0.3s ease;
}

.event-image img:hover {
    transform: scale(1.05);
}

.event-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// تحديث الإحصائيات
function updateEventStats() {
    fetch('/api/events/stats')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('motionEvents').textContent = data.stats.motion_count;
            document.getElementById('faceEvents').textContent = data.stats.face_count;
            document.getElementById('alertEvents').textContent = data.stats.alert_count;
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// عرض تفاصيل الحدث
function viewEventDetails(eventId) {
    fetch(`/api/events/${eventId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const event = data.event;
            const content = document.getElementById('eventDetailsContent');
            content.innerHTML = `
                <div class="row">
                    <div class="col-6"><strong>نوع الحدث:</strong></div>
                    <div class="col-6">${event.event_type}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>الكاميرا:</strong></div>
                    <div class="col-6">كاميرا ${event.camera_id}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>الوقت:</strong></div>
                    <div class="col-6">${event.created_at}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>الوصف:</strong></div>
                    <div class="col-6">${event.description || 'لا يوجد وصف'}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>مستوى الثقة:</strong></div>
                    <div class="col-6">${event.confidence ? (event.confidence * 100).toFixed(1) + '%' : 'غير محدد'}</div>
                </div>
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('eventDetailsModal'));
            modal.show();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في جلب تفاصيل الحدث', 'error');
    });
}

// عرض صورة الحدث
function showEventImage(imagePath) {
    const img = document.getElementById('eventImageDisplay');
    img.src = `/static/events/${imagePath}`;
    
    const modal = new bootstrap.Modal(document.getElementById('eventImageModal'));
    modal.show();
}

// تحميل صورة الحدث
function downloadEventImage(eventId) {
    window.open(`/api/events/${eventId}/image`, '_blank');
}

// حذف حدث
function deleteEvent(eventId) {
    if (confirm('هل أنت متأكد من حذف هذا الحدث؟')) {
        fetch(`/api/events/${eventId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف الحدث بنجاح', 'success');
                location.reload();
            } else {
                showAlert(data.message || 'فشل في حذف الحدث', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في حذف الحدث', 'error');
        });
    }
}

// تحديد جميع الأحداث كمقروءة
function markAllAsRead() {
    fetch('/api/events/mark-all-read', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم تحديد جميع الأحداث كمقروءة', 'success');
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في تحديث الأحداث', 'error');
    });
}

// مسح جميع الأحداث
function clearAllEvents() {
    if (confirm('هل أنت متأكد من حذف جميع الأحداث؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch('/api/events/clear-all', {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف جميع الأحداث', 'success');
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في حذف الأحداث', 'error');
        });
    }
}

// تحديث الأحداث
function refreshEvents() {
    location.reload();
}

// عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateEventStats();
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateEventStats, 30000);
    
    // إضافة مستمعات للفلاتر
    document.getElementById('searchEvents').addEventListener('input', filterEvents);
    document.getElementById('filterEventType').addEventListener('change', filterEvents);
    document.getElementById('filterCamera').addEventListener('change', filterEvents);
    document.getElementById('filterDate').addEventListener('change', filterEvents);
});

// فلترة الأحداث
function filterEvents() {
    // تنفيذ الفلترة هنا
    console.log('تطبيق فلاتر الأحداث...');
}
</script>
{% endblock %}
