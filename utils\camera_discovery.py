#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة اكتشاف الكاميرات التلقائي
Camera Auto-Discovery Module
"""

import socket
import threading
import time
import requests
import cv2
import logging
from typing import List, Dict, Optional
import ipaddress
import subprocess
import re
from urllib.parse import urlparse
import xml.etree.ElementTree as ET

logger = logging.getLogger(__name__)

class CameraDiscovery:
    """فئة اكتشاف الكاميرات التلقائي"""
    
    def __init__(self):
        self.discovered_cameras = []
        self.scan_timeout = 2
        self.max_threads = 50
        
        # منافذ شائعة للكاميرات
        self.common_ports = {
            'rtsp': [554, 8554, 1935],
            'http': [80, 8080, 8000, 8888, 9000],
            'onvif': [80, 8080, 8000, 3702]
        }
        
        # مسارات RTSP شائعة
        self.rtsp_paths = [
            '/stream1',
            '/cam/realmonitor?channel=1&subtype=0',
            '/videoMain',
            '/live/ch1',
            '/h264',
            '/video.cgi',
            '/mjpeg',
            '/axis-media/media.amp',
            '/onvif1',
            '/streaming/channels/1'
        ]
        
        # مسارات HTTP شائعة
        self.http_paths = [
            '/mjpeg',
            '/video.cgi',
            '/snapshot.cgi',
            '/image.jpg',
            '/axis-media/media.amp',
            '/cgi-bin/mjpg/video.cgi'
        ]
        
        # بيانات اعتماد افتراضية شائعة
        self.default_credentials = [
            ('admin', 'admin'),
            ('admin', ''),
            ('admin', '123456'),
            ('admin', 'password'),
            ('root', 'root'),
            ('user', 'user'),
            ('', ''),
            ('admin', '12345'),
            ('admin', 'admin123')
        ]
    
    def scan_network_range(self, network_range: str, protocols: Dict[str, bool] = None) -> List[Dict]:
        """فحص نطاق شبكة للبحث عن كاميرات"""
        if protocols is None:
            protocols = {'rtsp': True, 'http': True, 'onvif': True}
        
        self.discovered_cameras = []
        
        try:
            # تحليل نطاق الشبكة
            if '-' in network_range:
                # نطاق مثل ***********-254
                base_ip, end_range = network_range.rsplit('.', 1)
                start_ip, end_ip = end_range.split('-')
                start_ip = int(start_ip)
                end_ip = int(end_ip)
                
                ips_to_scan = []
                for i in range(start_ip, end_ip + 1):
                    ips_to_scan.append(f"{base_ip}.{i}")
            else:
                # شبكة CIDR مثل ***********/24
                network = ipaddress.ip_network(network_range, strict=False)
                ips_to_scan = [str(ip) for ip in network.hosts()]
            
            logger.info(f"بدء فحص {len(ips_to_scan)} عنوان IP")
            
            # فحص متوازي
            threads = []
            for ip in ips_to_scan:
                if len(threads) >= self.max_threads:
                    # انتظار انتهاء بعض الخيوط
                    for t in threads[:10]:
                        t.join()
                    threads = threads[10:]
                
                thread = threading.Thread(target=self._scan_ip, args=(ip, protocols))
                thread.daemon = True
                thread.start()
                threads.append(thread)
            
            # انتظار انتهاء جميع الخيوط
            for thread in threads:
                thread.join()
            
            logger.info(f"تم اكتشاف {len(self.discovered_cameras)} كاميرا")
            return self.discovered_cameras
            
        except Exception as e:
            logger.error(f"خطأ في فحص الشبكة: {str(e)}")
            return []
    
    def _scan_ip(self, ip: str, protocols: Dict[str, bool]):
        """فحص عنوان IP واحد"""
        try:
            # فحص RTSP
            if protocols.get('rtsp', False):
                self._scan_rtsp(ip)
            
            # فحص HTTP
            if protocols.get('http', False):
                self._scan_http(ip)
            
            # فحص ONVIF
            if protocols.get('onvif', False):
                self._scan_onvif(ip)
                
        except Exception as e:
            logger.debug(f"خطأ في فحص {ip}: {str(e)}")
    
    def _scan_rtsp(self, ip: str):
        """فحص بروتوكول RTSP"""
        for port in self.common_ports['rtsp']:
            if self._is_port_open(ip, port):
                for path in self.rtsp_paths:
                    for username, password in self.default_credentials:
                        try:
                            if username and password:
                                url = f"rtsp://{username}:{password}@{ip}:{port}{path}"
                            else:
                                url = f"rtsp://{ip}:{port}{path}"
                            
                            if self._test_rtsp_stream(url):
                                camera_info = {
                                    'ip': ip,
                                    'port': port,
                                    'type': 'rtsp',
                                    'url': url,
                                    'path': path,
                                    'username': username,
                                    'password': password,
                                    'status': 'online',
                                    'manufacturer': self._detect_manufacturer(ip),
                                    'model': None
                                }
                                self.discovered_cameras.append(camera_info)
                                logger.info(f"اكتشاف كاميرا RTSP: {url}")
                                return  # توقف عند أول نجاح
                        except Exception:
                            continue
    
    def _scan_http(self, ip: str):
        """فحص بروتوكول HTTP"""
        for port in self.common_ports['http']:
            if self._is_port_open(ip, port):
                for path in self.http_paths:
                    for username, password in self.default_credentials:
                        try:
                            url = f"http://{ip}:{port}{path}"
                            
                            if self._test_http_stream(url, username, password):
                                camera_info = {
                                    'ip': ip,
                                    'port': port,
                                    'type': 'http',
                                    'url': url,
                                    'path': path,
                                    'username': username,
                                    'password': password,
                                    'status': 'online',
                                    'manufacturer': self._detect_manufacturer(ip),
                                    'model': None
                                }
                                self.discovered_cameras.append(camera_info)
                                logger.info(f"اكتشاف كاميرا HTTP: {url}")
                                return
                        except Exception:
                            continue
    
    def _scan_onvif(self, ip: str):
        """فحص بروتوكول ONVIF"""
        for port in self.common_ports['onvif']:
            if self._is_port_open(ip, port):
                try:
                    onvif_url = f"http://{ip}:{port}/onvif/device_service"
                    
                    if self._test_onvif_device(onvif_url):
                        camera_info = {
                            'ip': ip,
                            'port': port,
                            'type': 'onvif',
                            'url': onvif_url,
                            'path': '/onvif/device_service',
                            'username': '',
                            'password': '',
                            'status': 'online',
                            'manufacturer': self._detect_manufacturer(ip),
                            'model': None
                        }
                        self.discovered_cameras.append(camera_info)
                        logger.info(f"اكتشاف جهاز ONVIF: {onvif_url}")
                        return
                except Exception:
                    continue
    
    def _is_port_open(self, ip: str, port: int) -> bool:
        """فحص ما إذا كان المنفذ مفتوحاً"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.scan_timeout)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def _test_rtsp_stream(self, url: str) -> bool:
        """اختبار بث RTSP"""
        try:
            cap = cv2.VideoCapture(url)
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                return ret and frame is not None
            return False
        except Exception:
            return False
    
    def _test_http_stream(self, url: str, username: str = '', password: str = '') -> bool:
        """اختبار بث HTTP"""
        try:
            auth = None
            if username and password:
                auth = (username, password)
            
            response = requests.get(url, auth=auth, timeout=self.scan_timeout, stream=True)
            
            # فحص نوع المحتوى
            content_type = response.headers.get('content-type', '').lower()
            
            return (response.status_code == 200 and 
                   ('image' in content_type or 'video' in content_type or 
                    'multipart' in content_type))
        except Exception:
            return False
    
    def _test_onvif_device(self, url: str) -> bool:
        """اختبار جهاز ONVIF"""
        try:
            # طلب GetDeviceInformation
            soap_body = """<?xml version="1.0" encoding="UTF-8"?>
            <soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
                <soap:Body>
                    <tds:GetDeviceInformation xmlns:tds="http://www.onvif.org/ver10/device/wsdl"/>
                </soap:Body>
            </soap:Envelope>"""
            
            headers = {
                'Content-Type': 'application/soap+xml',
                'SOAPAction': 'http://www.onvif.org/ver10/device/wsdl/GetDeviceInformation'
            }
            
            response = requests.post(url, data=soap_body, headers=headers, timeout=self.scan_timeout)
            return response.status_code == 200 and 'soap' in response.text.lower()
        except Exception:
            return False
    
    def _detect_manufacturer(self, ip: str) -> str:
        """محاولة اكتشاف الشركة المصنعة"""
        try:
            # محاولة الوصول لصفحة الويب الرئيسية
            response = requests.get(f"http://{ip}", timeout=2)
            content = response.text.lower()
            
            # البحث عن كلمات مفتاحية للشركات المصنعة
            manufacturers = {
                'dahua': ['dahua', 'dh-'],
                'hikvision': ['hikvision', 'hik'],
                'axis': ['axis'],
                'bosch': ['bosch'],
                'sony': ['sony'],
                'panasonic': ['panasonic'],
                'samsung': ['samsung'],
                'vivotek': ['vivotek'],
                'foscam': ['foscam'],
                'tp-link': ['tp-link', 'tplink']
            }
            
            for manufacturer, keywords in manufacturers.items():
                for keyword in keywords:
                    if keyword in content:
                        return manufacturer.title()
            
            return 'غير محدد'
        except Exception:
            return 'غير محدد'
    
    def detect_channels(self, camera_url: str, username: str = '', password: str = '') -> List[Dict]:
        """اكتشاف القنوات المتاحة في الكاميرا"""
        channels = []
        
        try:
            parsed_url = urlparse(camera_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            # قنوات شائعة للكاميرات
            common_channels = [
                {'number': 1, 'path': '/stream1', 'name': 'القناة الرئيسية'},
                {'number': 2, 'path': '/stream2', 'name': 'القناة الفرعية'},
                {'number': 1, 'path': '/cam/realmonitor?channel=1&subtype=0', 'name': 'HD الرئيسية'},
                {'number': 2, 'path': '/cam/realmonitor?channel=1&subtype=1', 'name': 'SD الفرعية'},
                {'number': 1, 'path': '/videoMain', 'name': 'الفيديو الرئيسي'},
                {'number': 2, 'path': '/videoSub', 'name': 'الفيديو الفرعي'},
            ]
            
            for channel in common_channels:
                test_url = base_url + channel['path']
                
                if username and password:
                    # إدراج بيانات الاعتماد في URL
                    test_url = test_url.replace('://', f'://{username}:{password}@')
                
                if self._test_rtsp_stream(test_url):
                    channel_info = {
                        'number': channel['number'],
                        'name': channel['name'],
                        'url': test_url,
                        'resolution': self._get_stream_resolution(test_url)
                    }
                    channels.append(channel_info)
            
            return channels
            
        except Exception as e:
            logger.error(f"خطأ في اكتشاف القنوات: {str(e)}")
            return []
    
    def _get_stream_resolution(self, url: str) -> Optional[str]:
        """الحصول على دقة البث"""
        try:
            cap = cv2.VideoCapture(url)
            if cap.isOpened():
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                cap.release()
                
                if width > 0 and height > 0:
                    return f"{width}x{height}"
            return None
        except Exception:
            return None
    
    def scan_local_network(self) -> List[Dict]:
        """فحص الشبكة المحلية تلقائياً"""
        try:
            # الحصول على عنوان IP المحلي
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            
            # تحديد نطاق الشبكة
            ip_parts = local_ip.split('.')
            network_base = '.'.join(ip_parts[:3])
            network_range = f"{network_base}.1-254"
            
            logger.info(f"فحص الشبكة المحلية: {network_range}")
            
            return self.scan_network_range(network_range)
            
        except Exception as e:
            logger.error(f"خطأ في فحص الشبكة المحلية: {str(e)}")
            return []


# مثال على الاستخدام
if __name__ == "__main__":
    discovery = CameraDiscovery()
    
    # فحص الشبكة المحلية
    cameras = discovery.scan_local_network()
    
    print(f"تم اكتشاف {len(cameras)} كاميرا:")
    for camera in cameras:
        print(f"- {camera['type'].upper()}: {camera['url']} ({camera['manufacturer']})")
    
    # اختبار اكتشاف القنوات
    if cameras:
        first_camera = cameras[0]
        channels = discovery.detect_channels(
            first_camera['url'], 
            first_camera['username'], 
            first_camera['password']
        )
        print(f"\nالقنوات المكتشفة للكاميرا الأولى: {len(channels)}")
        for channel in channels:
            print(f"- قناة {channel['number']}: {channel['name']} ({channel['resolution']})")
