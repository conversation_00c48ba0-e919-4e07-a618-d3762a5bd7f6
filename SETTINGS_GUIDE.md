# 🔧 دليل الإعدادات الشامل
## نظام المراقبة الذكي - إعدادات متقدمة

---

## 📋 **نظرة عامة**

تم إنشاء صفحة إعدادات شاملة ومتقدمة تتضمن جميع جوانب النظام مع واجهة سهلة الاستخدام وإدارة متكاملة.

---

## 🎛️ **أقسام الإعدادات**

### **1. الإعدادات العامة**
- ✅ **اسم النظام** - تخصيص اسم النظام
- ✅ **اللغة** - العربية، الإنجليزية، الفرنسية
- ✅ **المنطقة الزمنية** - إعداد التوقيت المحلي
- ✅ **تنسيق التاريخ** - خيارات متعددة للتاريخ
- ✅ **التحديث التلقائي** - فترة تحديث البيانات
- ✅ **الحد الأقصى للكاميرات** - عدد الكاميرات المسموح
- ✅ **الوضع المظلم** - تفعيل/إلغاء الوضع المظلم
- ✅ **الأصوات** - تفعيل التنبيهات الصوتية

### **2. إعدادات الخادم**
- ✅ **عنوان الخادم** - IP address للخادم
- ✅ **منفذ الخادم** - رقم المنفذ
- ✅ **الحد الأقصى للاتصالات** - عدد الاتصالات المتزامنة
- ✅ **انتهاء الجلسة** - مدة الجلسة بالدقائق
- ✅ **SSL/HTTPS** - تفعيل التشفير
- ✅ **وضع التطوير** - Debug mode
- ✅ **مستوى السجلات** - تفصيل السجلات

### **3. إعدادات الكاميرات**
- ✅ **الدقة الافتراضية** - 1080p, 720p, 480p
- ✅ **معدل الإطارات** - 30, 25, 15, 10 FPS
- ✅ **جودة البث** - عالية، متوسطة، منخفضة
- ✅ **مهلة الاتصال** - timeout بالثواني
- ✅ **عدد المحاولات** - إعادة المحاولة
- ✅ **حجم المخزن المؤقت** - buffer size
- ✅ **إعادة الاتصال التلقائي** - auto-reconnect
- ✅ **تحسين الصورة** - image enhancement

### **4. إعدادات التسجيل**
- ✅ **مجلد التسجيلات** - مسار حفظ الملفات
- ✅ **تنسيق التسجيل** - MP4, AVI, MKV
- ✅ **جودة التسجيل** - عالية، متوسطة، منخفضة
- ✅ **مدة المقطع** - تقسيم التسجيلات
- ✅ **الحد الأقصى للتخزين** - بالجيجابايت
- ✅ **مدة الاحتفاظ** - بالأيام
- ✅ **التسجيل التلقائي** - عند كشف الحركة
- ✅ **التسجيل المسبق** - قبل الحدث
- ✅ **التنظيف التلقائي** - حذف القديم

### **5. كشف الحركة والذكاء الاصطناعي**

#### **كشف الحركة:**
- ✅ **حساسية كشف الحركة** - مقياس 1-10
- ✅ **عتبة كشف الحركة** - نسبة مئوية
- ✅ **تفعيل كشف الحركة** - تشغيل/إيقاف

#### **كشف الوجوه:**
- ✅ **دقة كشف الوجوه** - نسبة الثقة
- ✅ **نموذج كشف الوجوه** - HOG (سريع) أو CNN (دقيق)
- ✅ **تفعيل كشف الوجوه** - تشغيل/إيقاف
- ✅ **التعرف على الوجوه** - face recognition

#### **كشف الأشياء (YOLO):**
- ✅ **نموذج YOLO** - YOLOv5s, YOLOv5m, YOLOv5l
- ✅ **دقة كشف الأشياء** - نسبة الثقة
- ✅ **الأشياء المراد كشفها** - أشخاص، سيارات، حيوانات
- ✅ **تفعيل كشف الأشياء** - تشغيل/إيقاف

### **6. الإشعارات والتنبيهات**

#### **البريد الإلكتروني:**
- ✅ **خادم SMTP** - إعدادات البريد
- ✅ **منفذ SMTP** - رقم المنفذ
- ✅ **بيانات الاعتماد** - اسم المستخدم وكلمة المرور
- ✅ **قائمة المستقبلين** - عناوين البريد

#### **Telegram:**
- ✅ **Bot Token** - رمز البوت
- ✅ **Chat ID** - معرف المحادثة

#### **أنواع الإشعارات:**
- ✅ **كشف الحركة** - تنبيهات الحركة
- ✅ **كشف الوجوه** - تنبيهات الوجوه
- ✅ **كشف الأشياء** - تنبيهات الأشياء
- ✅ **أحداث النظام** - تنبيهات النظام
- ✅ **انقطاع الاتصال** - تنبيهات الشبكة
- ✅ **امتلاء التخزين** - تنبيهات المساحة

### **7. إدارة التخزين**
- ✅ **معلومات التخزين** - المساحة المستخدمة والمتاحة
- ✅ **شريط التقدم** - نسبة الاستخدام
- ✅ **عتبة التنظيف** - بدء التنظيف التلقائي
- ✅ **استراتيجية التنظيف** - الأقدم، الأكبر، الأقل جودة
- ✅ **أدوات التنظيف** - حذف التسجيلات، الملفات المؤقتة، السجلات

### **8. الأمان والخصوصية**

#### **سياسة كلمات المرور:**
- ✅ **الحد الأدنى لطول كلمة المرور** - عدد الأحرف
- ✅ **انتهاء صلاحية كلمة المرور** - بالأيام
- ✅ **كلمة مرور معقدة** - أحرف وأرقام ورموز
- ✅ **المصادقة الثنائية** - 2FA

#### **إعدادات الجلسات:**
- ✅ **الحد الأقصى لمحاولات الدخول** - عدد المحاولات
- ✅ **مدة الحظر** - بالدقائق
- ✅ **تسجيل الجلسات** - مراقبة النشاط

#### **أمان الشبكة:**
- ✅ **عناوين IP المسموحة** - قائمة بيضاء
- ✅ **تفعيل قائمة IP البيضاء** - تقييد الوصول
- ✅ **حد معدل الطلبات** - منع الهجمات

#### **التشفير:**
- ✅ **تشفير ملفات التسجيل** - حماية الملفات
- ✅ **تشفير قاعدة البيانات** - حماية البيانات

### **9. إعدادات الشبكة**

#### **الاكتشاف التلقائي:**
- ✅ **نطاق الشبكة الافتراضي** - IP range
- ✅ **مهلة الفحص** - timeout بالثواني
- ✅ **المنافذ الافتراضية** - قائمة المنافذ

#### **البروتوكولات المدعومة:**
- ✅ **RTSP** - تفعيل/إلغاء
- ✅ **HTTP/MJPEG** - تفعيل/إلغاء
- ✅ **ONVIF** - تفعيل/إلغاء

#### **إعدادات الاتصال:**
- ✅ **حجم مجموعة الاتصالات** - connection pool
- ✅ **فترة Keep-Alive** - بالثواني
- ✅ **مراقبة الشبكة** - network monitoring

### **10. النسخ الاحتياطي والاستعادة**

#### **النسخ الاحتياطي التلقائي:**
- ✅ **تكرار النسخ** - يومي، أسبوعي، شهري
- ✅ **وقت النسخ** - الساعة المحددة
- ✅ **مجلد النسخ** - مسار الحفظ
- ✅ **الحد الأقصى للنسخ** - عدد النسخ المحفوظة
- ✅ **مستوى الضغط** - بدون، منخفض، متوسط، عالي

#### **أدوات النسخ الاحتياطي:**
- ✅ **إنشاء نسخة احتياطية** - فوري
- ✅ **استعادة من نسخة** - اختيار ملف
- ✅ **تصدير الإعدادات** - حفظ الإعدادات
- ✅ **استيراد الإعدادات** - تحميل الإعدادات

#### **قائمة النسخ الاحتياطية:**
- ✅ **عرض النسخ المتاحة** - التاريخ والحجم
- ✅ **استعادة نسخة محددة** - اختيار نسخة
- ✅ **حذف نسخة** - إدارة المساحة

---

## 🚀 **كيفية الاستخدام**

### **الوصول للإعدادات:**
1. تسجيل الدخول كمدير (admin)
2. الانتقال إلى صفحة "الإعدادات"
3. اختيار القسم المطلوب من القائمة الجانبية

### **حفظ الإعدادات:**
- **حفظ جميع الإعدادات** - زر في أعلى الصفحة
- **حفظ تلقائي** - عند تغيير أي إعداد
- **استعادة افتراضي** - إعادة تعيين جميع الإعدادات

### **تصدير/استيراد:**
- **تصدير** - حفظ الإعدادات في ملف JSON
- **استيراد** - تحميل إعدادات من ملف
- **مشاركة الإعدادات** - بين أنظمة متعددة

---

## 🔧 **الإعدادات المتقدمة**

### **ملف الإعدادات (config.json):**
```json
{
  "general": { ... },
  "server": { ... },
  "cameras": { ... },
  "recording": { ... },
  "detection": { ... },
  "notifications": { ... },
  "storage": { ... },
  "security": { ... },
  "network": { ... },
  "backup": { ... }
}
```

### **API Endpoints:**
- `GET /api/settings` - جلب الإعدادات
- `POST /api/settings` - حفظ الإعدادات
- `POST /api/settings/reset` - استعادة افتراضي
- `GET /api/settings/export` - تصدير الإعدادات
- `POST /api/settings/import` - استيراد الإعدادات

---

## 💡 **نصائح مهمة**

### **الأمان:**
- ✅ استخدم كلمات مرور قوية
- ✅ فعّل المصادقة الثنائية
- ✅ قيّد الوصول بعناوين IP
- ✅ فعّل تشفير البيانات

### **الأداء:**
- ✅ اضبط جودة البث حسب الشبكة
- ✅ استخدم إعدادات مناسبة للأجهزة
- ✅ راقب استهلاك التخزين
- ✅ نظّف الملفات القديمة دورياً

### **النسخ الاحتياطي:**
- ✅ فعّل النسخ الاحتياطي التلقائي
- ✅ احتفظ بنسخ متعددة
- ✅ اختبر استعادة النسخ دورياً
- ✅ احفظ النسخ في مكان آمن

---

## 🎉 **الخلاصة**

تم إنشاء نظام إعدادات شامل ومتقدم يغطي جميع جوانب نظام المراقبة الذكي مع:

✅ **واجهة سهلة ومنظمة**
✅ **إعدادات شاملة ومفصلة**
✅ **أدوات إدارة متقدمة**
✅ **نظام نسخ احتياطي متكامل**
✅ **أمان وحماية عالية**

**النظام الآن جاهز للاستخدام المهني! 🚀**
