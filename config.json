{"general": {"system_name": "نظام المراقبة الذكي", "language": "ar", "timezone": "Asia/Riyadh", "date_format": "YYYY-MM-DD", "auto_refresh": 30, "max_cameras": 16, "dark_mode": true, "enable_sounds": true}, "server": {"host": "0.0.0.0", "port": 5000, "max_connections": 100, "session_timeout": 60, "enable_ssl": false, "debug": false, "log_level": "INFO", "secret_key": "surveillance-secret-key-2024"}, "cameras": {"default_resolution": "1920x1080", "default_fps": 25, "stream_quality": "high", "connection_timeout": 10, "max_retries": 3, "buffer_size": 1, "auto_reconnect": true, "enhance_image": true}, "recording": {"path": "./recordings", "format": "mp4", "quality": "high", "segment_duration": 10, "max_storage_gb": 100, "retention_days": 30, "auto_recording": true, "pre_recording": false, "auto_cleanup": true}, "detection": {"motion_sensitivity": 5, "motion_threshold": 15, "enable_motion": true, "face_confidence": 80, "face_model": "hog", "enable_face": true, "enable_face_recognition": false, "yolo_model": "yolov5s", "object_confidence": 70, "detect_person": true, "detect_car": true, "detect_animal": false, "enable_object": false}, "notifications": {"email": {"enabled": false, "smtp_server": "", "smtp_port": 587, "username": "", "password": "", "recipients": []}, "telegram": {"enabled": false, "bot_token": "", "chat_id": ""}, "types": {"motion": true, "face": true, "object": false, "system": true, "connection": true, "storage": true}}, "storage": {"cleanup_threshold": 80, "cleanup_strategy": "oldest", "auto_cleanup": true}, "security": {"min_password_length": 8, "password_expiry": 90, "require_complex_password": true, "enable_two_factor": false, "max_login_attempts": 5, "lockout_duration": 15, "enable_session_logging": true, "allowed_ips": [], "enable_ip_whitelist": false, "enable_rate_limit": true, "encrypt_recordings": false, "encrypt_database": false}, "network": {"default_network_range": "***********-254", "scan_timeout": 5, "default_ports": "554,8080,80,8000,8554", "enable_rtsp": true, "enable_http": true, "enable_onvif": true, "connection_pool_size": 10, "keep_alive_interval": 30, "enable_network_monitoring": true}, "backup": {"frequency": "daily", "time": "02:00", "path": "./backups", "max_backups": 7, "compression_level": "medium", "auto_backup": true}, "database": {"path": "database.db"}}