# 🎥 نظام المراقبة الذكي | Smart Surveillance System

نظام مراقبة متقدم مشابه لجهاز Dahua DHI-XVR5116H مطور بلغة Python ويعمل على الويب مع دعم الذكاء الاصطناعي.

## ✨ الميزات الرئيسية

### 📺 البث المباشر
- دعم حتى 16 كاميرا متزامنة
- بث عالي الجودة (1080p)
- دعم بروتوكولات متعددة: RTSP, HTTP, USB, ONVIF
- واجهة عرض متعددة الشبكات (1×1, 2×2, 3×3, 4×4)
- عرض بملء الشاشة لكل كاميرا

### 🤖 الذكاء الاصطناعي
- **التعرف على الوجوه**: كشف وتحديد الأشخاص المعروفين
- **كشف الحركة الذكي**: تنبيهات فورية عند اكتشاف حركة
- **كشف الأشخاص والمركبات**: باستخدام YOLOv8
- **مناطق الحماية**: تحديد مناطق مراقبة خاصة

### 📹 التسجيل والأرشفة
- تسجيل تلقائي عند كشف الحركة
- تسجيل مجدول (24/7 أو حسب الجدول)
- تسجيل يدوي لكل كاميرا
- ضغط ذكي وإدارة المساحة
- نسخ احتياطي تلقائي

### 🔔 التنبيهات والإشعارات
- تنبيهات فورية عبر البريد الإلكتروني
- إشعارات Telegram
- تنبيهات صوتية في المتصفح
- سجل شامل للأحداث

### 🛡️ الأمان والصلاحيات
- نظام مستخدمين متعدد المستويات
- تشفير كلمات المرور
- جلسات آمنة
- سجل دخول وأنشطة

### 🌐 واجهة ويب عربية
- تصميم عصري ومتجاوب
- دعم كامل للغة العربية
- واجهة سهلة الاستخدام
- تحكم كامل عبر المتصفح

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
```bash
Python 3.8+
OpenCV 4.5+
Flask 2.0+
```

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/smart-surveillance-system.git
cd smart-surveillance-system
```

### 2. تثبيت المكتبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل النظام
```bash
python app.py
```

### 4. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost:5000`

**بيانات الدخول الافتراضية:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## 📁 هيكل المشروع

```
smart_xvr_system/
│
├── app.py                  # التطبيق الرئيسي
├── config.json             # ملف الإعدادات
├── requirements.txt        # المكتبات المطلوبة
├── README.md              # دليل المستخدم
│
├── templates/             # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── index.html        # الصفحة الرئيسية
│   ├── login.html        # صفحة تسجيل الدخول
│   ├── cameras.html      # إدارة الكاميرات
│   └── ...
│
├── static/               # ملفات CSS/JS/Images
│   ├── css/
│   ├── js/
│   └── images/
│
├── stream/               # وحدة البث
│   ├── __init__.py
│   └── camera_manager.py # مدير الكاميرات
│
├── ai_modules/           # وحدات الذكاء الاصطناعي
│   ├── __init__.py
│   ├── face_recognition.py
│   ├── object_detection.py
│   └── motion_detection.py
│
├── recordings/           # مجلد التسجيلات
├── logs/                # ملفات السجلات
├── known_faces/         # صور الوجوه المعروفة
└── database/            # قاعدة البيانات
```

## ⚙️ الإعدادات

### إضافة كاميرات جديدة

#### كاميرا RTSP
```json
{
  "name": "كاميرا المدخل الرئيسي",
  "url": "rtsp://*************:554/stream1",
  "type": "rtsp",
  "username": "admin",
  "password": "password123"
}
```

#### كاميرا USB
```json
{
  "name": "كاميرا USB",
  "url": "0",
  "type": "usb"
}
```

#### كاميرا IP
```json
{
  "name": "كاميرا IP",
  "url": "http://*************/mjpeg",
  "type": "http"
}
```

### تكوين التنبيهات

#### البريد الإلكتروني
```json
{
  "email": {
    "enabled": true,
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "your-app-password",
    "recipients": ["<EMAIL>"]
  }
}
```

#### Telegram
```json
{
  "telegram": {
    "enabled": true,
    "bot_token": "YOUR_BOT_TOKEN",
    "chat_ids": ["CHAT_ID_1", "CHAT_ID_2"]
  }
}
```

## 🔧 الاستخدام

### 1. إضافة كاميرات
- انتقل إلى قسم "الكاميرات"
- اضغط "إضافة كاميرا جديدة"
- أدخل بيانات الكاميرا
- اختبر الاتصال

### 2. تفعيل التعرف على الوجوه
- انتقل إلى قسم "الذكاء الاصطناعي"
- ارفع صور الأشخاص المعروفين
- فعل خاصية التعرف على الوجوه

### 3. إعداد التسجيل
- حدد نمط التسجيل (تلقائي/مجدول/يدوي)
- اختر جودة التسجيل
- حدد مدة الاحتفاظ بالملفات

### 4. تكوين التنبيهات
- فعل كشف الحركة
- أضف عناوين البريد الإلكتروني
- اربط بوت Telegram

## 📊 لوحة التحكم

### الإحصائيات المباشرة
- عدد الكاميرات النشطة
- حالة الاتصال لكل كاميرا
- عدد التسجيلات اليومية
- التنبيهات الجديدة

### التحكم السريع
- بدء/إيقاف التسجيل للكل
- تحديث البث
- تبديل كشف الحركة
- تغيير تخطيط الشبكة

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

#### الكاميرا لا تظهر
1. تأكد من صحة رابط RTSP
2. تحقق من بيانات الاعتماد
3. تأكد من إمكانية الوصول للشبكة

#### بطء في البث
1. قلل جودة البث
2. قلل عدد الكاميرات المتزامنة
3. تحقق من سرعة الشبكة

#### مشاكل التعرف على الوجوه
1. تأكد من وضوح الصور المرجعية
2. اضبط مستوى الحساسية
3. تحقق من الإضاءة

## 🔄 التحديثات المستقبلية

- [ ] دعم كاميرات PTZ (التحكم بالحركة)
- [ ] تطبيق موبايل
- [ ] تحليلات متقدمة بالذكاء الاصطناعي
- [ ] دعم التخزين السحابي
- [ ] واجهة برمجة تطبيقات REST API
- [ ] دعم عدة مواقع جغرافية

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 💬 Telegram: @SurveillanceSupport
- 🐛 GitHub Issues: [رابط المشاكل]

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## ⭐ إذا أعجبك المشروع

لا تنس إعطاء المشروع نجمة ⭐ ومشاركته مع الآخرين!

---

**تم تطويره بـ ❤️ في المملكة العربية السعودية**
