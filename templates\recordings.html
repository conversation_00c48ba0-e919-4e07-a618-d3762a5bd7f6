{% extends "base.html" %}

{% block title %}التسجيلات - نظام المراقبة الذكي{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-film me-2"></i>
                    إدارة التسجيلات
                </h2>
                <div class="btn-group" role="group">
                    <button class="btn btn-primary" onclick="startRecordingAll()">
                        <i class="fas fa-record-vinyl me-2"></i>
                        بدء التسجيل للكل
                    </button>
                    <button class="btn btn-danger" onclick="stopRecordingAll()">
                        <i class="fas fa-stop me-2"></i>
                        إيقاف التسجيل
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ recordings|length }}</div>
                        <div class="stats-label">إجمالي التسجيلات</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-film fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="todayRecordings">0</div>
                        <div class="stats-label">تسجيلات اليوم</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="activeRecordings">0</div>
                        <div class="stats-label">تسجيل نشط</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-record-vinyl fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card bg-info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number" id="totalSize">0 GB</div>
                        <div class="stats-label">الحجم الإجمالي</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أدوات التحكم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchRecordings" 
                                       placeholder="البحث في التسجيلات...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterCamera">
                                <option value="">جميع الكاميرات</option>
                                <!-- ستتم إضافة الكاميرات هنا -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterDate">
                                <option value="">جميع التواريخ</option>
                                <option value="today">اليوم</option>
                                <option value="yesterday">أمس</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="btn-group me-2" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="refreshRecordings()">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    تحديث
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="downloadSelected()">
                                    <i class="fas fa-download me-1"></i>
                                    تحميل المحدد
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteSelected()">
                                    <i class="fas fa-trash me-1"></i>
                                    حذف المحدد
                                </button>
                            </div>
                            
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-info" onclick="exportRecordings()">
                                    <i class="fas fa-file-export me-1"></i>
                                    تصدير القائمة
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="cleanupOldRecordings()">
                                    <i class="fas fa-broom me-1"></i>
                                    تنظيف القديم
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- قائمة التسجيلات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة التسجيلات
                    </h5>
                </div>
                <div class="card-body">
                    {% if recordings %}
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll">
                                        </div>
                                    </th>
                                    <th>الكاميرا</th>
                                    <th>اسم الملف</th>
                                    <th>تاريخ البداية</th>
                                    <th>المدة</th>
                                    <th>الحجم</th>
                                    <th>نوع التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="recordingsTableBody">
                                {% for recording in recordings %}
                                <tr data-recording-id="{{ recording.id }}">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input recording-checkbox" type="checkbox" 
                                                   value="{{ recording.id }}">
                                        </div>
                                    </td>
                                    <td>
                                        <strong>كاميرا {{ recording.camera_id }}</strong>
                                    </td>
                                    <td>
                                        <code>{{ recording.filename }}</code>
                                    </td>
                                    <td>
                                        {{ recording.start_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                    </td>
                                    <td>
                                        {% if recording.end_time %}
                                            {{ ((recording.end_time - recording.start_time).total_seconds() / 60)|round(1) }} دقيقة
                                        {% else %}
                                            <span class="badge bg-danger">جاري التسجيل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if recording.file_size %}
                                            {{ (recording.file_size / 1024 / 1024)|round(1) }} MB
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ recording.trigger_type or 'يدوي' }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary" onclick="playRecording({{ recording.id }})" title="تشغيل">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="downloadRecording({{ recording.id }})" title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="showRecordingInfo({{ recording.id }})" title="معلومات">
                                                <i class="fas fa-info"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteRecording({{ recording.id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- ترقيم الصفحات -->
                    <nav aria-label="ترقيم التسجيلات">
                        <ul class="pagination justify-content-center">
                            <li class="page-item">
                                <a class="page-link" href="#" aria-label="السابق">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#" aria-label="التالي">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-film fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد تسجيلات</h4>
                        <p class="text-muted">ابدأ بتسجيل الفيديو من الكاميرات</p>
                        <button class="btn btn-primary" onclick="startRecordingAll()">
                            <i class="fas fa-record-vinyl me-2"></i>
                            بدء التسجيل الآن
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تشغيل التسجيل -->
<div class="modal fade" id="playRecordingModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content bg-dark">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white" id="playRecordingTitle">تشغيل التسجيل</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div id="videoPlayer" class="w-100 h-100 d-flex align-items-center justify-content-center" style="min-height: 400px;">
                    <video id="recordingVideo" class="w-100" controls style="max-height: 70vh;">
                        <source src="" type="video/mp4">
                        متصفحك لا يدعم تشغيل الفيديو
                    </video>
                </div>
            </div>
            <div class="modal-footer border-0">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-light" onclick="seekVideo(-10)">
                        <i class="fas fa-backward me-1"></i>
                        -10 ثانية
                    </button>
                    <button type="button" class="btn btn-outline-light" onclick="togglePlayPause()">
                        <i class="fas fa-play me-1" id="playPauseIcon"></i>
                        تشغيل/إيقاف
                    </button>
                    <button type="button" class="btn btn-outline-light" onclick="seekVideo(10)">
                        <i class="fas fa-forward me-1"></i>
                        +10 ثانية
                    </button>
                </div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة معلومات التسجيل -->
<div class="modal fade" id="recordingInfoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات التسجيل
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="recordingInfoContent">
                <!-- ستتم إضافة المعلومات هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    border-radius: 12px;
    padding: 20px;
    color: white;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.table td {
    vertical-align: middle;
}

.recording-checkbox {
    cursor: pointer;
}

#recordingVideo {
    background: #000;
    border-radius: 8px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let selectedRecordings = [];

// تحديد/إلغاء تحديد جميع التسجيلات
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.recording-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedRecordings();
});

// تحديث قائمة التسجيلات المحددة
function updateSelectedRecordings() {
    const checkboxes = document.querySelectorAll('.recording-checkbox:checked');
    selectedRecordings = Array.from(checkboxes).map(cb => cb.value);
}

// بدء التسجيل لجميع الكاميرات
function startRecordingAll() {
    fetch('/api/recordings/start-all', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم بدء التسجيل لجميع الكاميرات', 'success');
            updateStats();
        } else {
            showAlert(data.message || 'فشل في بدء التسجيل', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في بدء التسجيل', 'error');
    });
}

// إيقاف التسجيل لجميع الكاميرات
function stopRecordingAll() {
    fetch('/api/recordings/stop-all', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إيقاف التسجيل لجميع الكاميرات', 'info');
            updateStats();
        } else {
            showAlert(data.message || 'فشل في إيقاف التسجيل', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في إيقاف التسجيل', 'error');
    });
}

// تشغيل تسجيل
function playRecording(recordingId) {
    const modal = new bootstrap.Modal(document.getElementById('playRecordingModal'));
    const video = document.getElementById('recordingVideo');
    
    // تحديد مصدر الفيديو
    video.src = `/api/recordings/${recordingId}/stream`;
    
    modal.show();
}

// تحميل تسجيل
function downloadRecording(recordingId) {
    window.open(`/api/recordings/${recordingId}/download`, '_blank');
}

// عرض معلومات التسجيل
function showRecordingInfo(recordingId) {
    fetch(`/api/recordings/${recordingId}/info`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const content = document.getElementById('recordingInfoContent');
            content.innerHTML = `
                <div class="row">
                    <div class="col-6"><strong>الكاميرا:</strong></div>
                    <div class="col-6">كاميرا ${data.recording.camera_id}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>اسم الملف:</strong></div>
                    <div class="col-6"><code>${data.recording.filename}</code></div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>تاريخ البداية:</strong></div>
                    <div class="col-6">${data.recording.start_time}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>تاريخ النهاية:</strong></div>
                    <div class="col-6">${data.recording.end_time || 'جاري التسجيل'}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>حجم الملف:</strong></div>
                    <div class="col-6">${data.recording.file_size_mb} MB</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>نوع التسجيل:</strong></div>
                    <div class="col-6">${data.recording.trigger_type}</div>
                </div>
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('recordingInfoModal'));
            modal.show();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في جلب معلومات التسجيل', 'error');
    });
}

// حذف تسجيل
function deleteRecording(recordingId) {
    if (confirm('هل أنت متأكد من حذف هذا التسجيل؟')) {
        fetch(`/api/recordings/${recordingId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف التسجيل بنجاح', 'success');
                location.reload();
            } else {
                showAlert(data.message || 'فشل في حذف التسجيل', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في حذف التسجيل', 'error');
        });
    }
}

// تحديث الإحصائيات
function updateStats() {
    fetch('/api/recordings/stats')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('todayRecordings').textContent = data.stats.today_count;
            document.getElementById('activeRecordings').textContent = data.stats.active_count;
            document.getElementById('totalSize').textContent = data.stats.total_size_gb + ' GB';
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateStats();
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStats, 30000);
    
    // إضافة مستمعات للفلاتر
    document.getElementById('searchRecordings').addEventListener('input', filterRecordings);
    document.getElementById('filterCamera').addEventListener('change', filterRecordings);
    document.getElementById('filterDate').addEventListener('change', filterRecordings);
});

// فلترة التسجيلات
function filterRecordings() {
    // تنفيذ الفلترة هنا
    console.log('تطبيق الفلاتر...');
}
</script>
{% endblock %}
