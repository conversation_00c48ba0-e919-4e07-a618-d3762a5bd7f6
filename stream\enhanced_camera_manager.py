#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الكاميرات المحسن - حل مشكلة البث المباشر
Enhanced Camera Manager - Fix live streaming issues
"""

import cv2
import threading
import time
import base64
import logging
from typing import Dict, Optional, Callable
import numpy as np
from queue import Queue, Empty
import json
from flask_socketio import emit

logger = logging.getLogger(__name__)

class EnhancedCameraStream:
    """فئة محسنة لإدارة بث كاميرا واحدة"""
    
    def __init__(self, camera_id: int, rtsp_url: str, username: str = None, password: str = None):
        self.camera_id = camera_id
        self.rtsp_url = rtsp_url
        self.username = username
        self.password = password
        
        # حالة البث
        self.is_active = False
        self.is_connected = False
        self.last_frame = None
        self.last_frame_time = 0
        
        # إعدادات الاتصال
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 3
        self.reconnect_delay = 2
        
        # خيوط المعالجة
        self.capture_thread = None
        self.broadcast_thread = None
        self.frame_queue = Queue(maxsize=5)
        
        # كائن الالتقاط
        self.cap = None
        
        # إحصائيات
        self.frame_count = 0
        self.fps = 0
        self.last_fps_time = time.time()
        self.start_time = None
        self.error_count = 0
        
        # قفل للحماية من التداخل
        self.lock = threading.Lock()
        
        # إعدادات الجودة
        self.jpeg_quality = 70
        self.frame_width = 640
        self.frame_height = 480
    
    def build_rtsp_url(self) -> str:
        """بناء رابط RTSP مع بيانات الاعتماد"""
        if self.username and self.password:
            if '://' in self.rtsp_url:
                protocol, rest = self.rtsp_url.split('://', 1)
                return f"{protocol}://{self.username}:{self.password}@{rest}"
            else:
                return f"rtsp://{self.username}:{self.password}@{self.rtsp_url}"
        return self.rtsp_url
    
    def start(self) -> bool:
        """بدء البث"""
        if self.is_active:
            return True
            
        try:
            self.is_active = True
            self.start_time = time.time()  # تسجيل وقت البدء

            # بدء خيط التقاط الإطارات
            self.capture_thread = threading.Thread(target=self._capture_frames, daemon=True)
            self.capture_thread.start()

            # بدء خيط البث
            self.broadcast_thread = threading.Thread(target=self._broadcast_frames, daemon=True)
            self.broadcast_thread.start()

            logger.info(f"تم بدء بث الكاميرا {self.camera_id}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في بدء بث الكاميرا {self.camera_id}: {e}")
            self.is_active = False
            return False
    
    def stop(self):
        """إيقاف البث"""
        self.is_active = False
        
        # إغلاق الكاميرا
        if self.cap:
            self.cap.release()
            self.cap = None
            
        # انتظار انتهاء الخيوط
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2)
            
        if self.broadcast_thread and self.broadcast_thread.is_alive():
            self.broadcast_thread.join(timeout=2)
            
        self.is_connected = False
        logger.info(f"تم إيقاف بث الكاميرا {self.camera_id}")
    
    def _capture_frames(self):
        """التقاط الإطارات في خيط منفصل"""
        while self.is_active:
            try:
                if not self._connect():
                    time.sleep(self.reconnect_delay)
                    continue
                
                ret, frame = self.cap.read()
                
                if not ret or frame is None:
                    logger.warning(f"فشل في قراءة إطار من الكاميرا {self.camera_id}")
                    self.error_count += 1
                    self._disconnect()
                    time.sleep(1)
                    continue
                
                # تحديث الإحصائيات
                self._update_stats()
                
                # تحسين الإطار
                frame = self._enhance_frame(frame)
                
                # حفظ الإطار الأخير
                with self.lock:
                    self.last_frame = frame.copy()
                    self.last_frame_time = time.time()
                
                # إضافة الإطار للطابور
                try:
                    if not self.frame_queue.full():
                        self.frame_queue.put(frame, timeout=0.1)
                    else:
                        # إزالة الإطار الأقدم إذا كان الطابور ممتلئ
                        try:
                            self.frame_queue.get_nowait()
                            self.frame_queue.put(frame, timeout=0.1)
                        except Empty:
                            pass
                except:
                    pass
                
                # تأخير للتحكم في معدل الإطارات
                time.sleep(0.033)  # ~30 FPS
                
            except Exception as e:
                logger.error(f"خطأ في التقاط إطارات الكاميرا {self.camera_id}: {e}")
                self._disconnect()
                time.sleep(2)
    
    def _broadcast_frames(self):
        """بث الإطارات عبر SocketIO"""
        from app import socketio  # استيراد محلي لتجنب التداخل الدائري
        
        while self.is_active:
            try:
                # الحصول على إطار من الطابور
                frame = self.frame_queue.get(timeout=1)
                
                # تحويل الإطار إلى JPEG
                jpeg_frame = self._frame_to_jpeg(frame)
                
                if jpeg_frame:
                    # تحويل إلى base64
                    frame_b64 = base64.b64encode(jpeg_frame).decode('utf-8')
                    
                    # إرسال الإطار عبر SocketIO
                    socketio.emit('camera_frame', {
                        'camera_id': self.camera_id,
                        'frame': frame_b64,
                        'timestamp': time.time(),
                        'fps': round(self.fps, 1)
                    }, namespace='/camera')
                
            except Empty:
                # لا توجد إطارات في الطابور
                continue
            except Exception as e:
                logger.error(f"خطأ في بث إطارات الكاميرا {self.camera_id}: {e}")
                time.sleep(0.1)
    
    def _connect(self) -> bool:
        """الاتصال بالكاميرا"""
        if self.is_connected and self.cap and self.cap.isOpened():
            return True
        
        try:
            if self.cap:
                self.cap.release()
            
            rtsp_url = self.build_rtsp_url()
            logger.info(f"محاولة الاتصال بالكاميرا {self.camera_id}: {rtsp_url}")
            
            self.cap = cv2.VideoCapture(rtsp_url)
            
            # إعدادات الالتقاط المحسنة
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
            
            # اختبار قراءة إطار
            ret, test_frame = self.cap.read()
            
            if self.cap.isOpened() and ret and test_frame is not None:
                self.is_connected = True
                self.reconnect_attempts = 0
                logger.info(f"تم الاتصال بالكاميرا {self.camera_id} بنجاح")
                return True
            else:
                raise Exception("فشل في فتح الكاميرا أو قراءة إطار اختبار")
                
        except Exception as e:
            self.reconnect_attempts += 1
            logger.error(f"فشل الاتصال بالكاميرا {self.camera_id} (المحاولة {self.reconnect_attempts}): {e}")
            
            if self.reconnect_attempts >= self.max_reconnect_attempts:
                logger.error(f"تم الوصول للحد الأقصى من محاولات الاتصال للكاميرا {self.camera_id}")
                # لا نوقف البث، بل نعيد المحاولة لاحقاً
                self.reconnect_attempts = 0
            
            return False
    
    def _disconnect(self):
        """قطع الاتصال"""
        self.is_connected = False
        if self.cap:
            self.cap.release()
            self.cap = None
    
    def _enhance_frame(self, frame):
        """تحسين جودة الإطار"""
        try:
            # تغيير حجم الإطار إذا لزم الأمر
            if frame.shape[1] != self.frame_width or frame.shape[0] != self.frame_height:
                frame = cv2.resize(frame, (self.frame_width, self.frame_height))
            
            # تحسين الإضاءة والتباين
            lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            l = clahe.apply(l)
            enhanced = cv2.merge([l, a, b])
            frame = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
            
            # إضافة معلومات الكاميرا
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            cv2.putText(frame, f"Camera {self.camera_id} | {timestamp}", 
                       (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            cv2.putText(frame, f"FPS: {self.fps:.1f}", 
                       (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return frame
        except Exception as e:
            logger.error(f"خطأ في تحسين الإطار للكاميرا {self.camera_id}: {e}")
            return frame
    
    def _frame_to_jpeg(self, frame, quality: int = None) -> Optional[bytes]:
        """تحويل الإطار إلى JPEG"""
        try:
            quality = quality or self.jpeg_quality
            encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
            _, buffer = cv2.imencode('.jpg', frame, encode_param)
            return buffer.tobytes()
        except Exception as e:
            logger.error(f"فشل في تحويل الإطار إلى JPEG للكاميرا {self.camera_id}: {e}")
            return None
    
    def _update_stats(self):
        """تحديث إحصائيات الأداء"""
        self.frame_count += 1
        current_time = time.time()
        
        if current_time - self.last_fps_time >= 1.0:
            self.fps = self.frame_count / (current_time - self.last_fps_time)
            self.frame_count = 0
            self.last_fps_time = current_time
    
    def get_latest_frame_jpeg(self) -> Optional[bytes]:
        """الحصول على أحدث إطار كـ JPEG"""
        with self.lock:
            if self.last_frame is not None and time.time() - self.last_frame_time < 5:
                return self._frame_to_jpeg(self.last_frame)
        return None
    
    def get_status(self) -> dict:
        """الحصول على حالة الكاميرا"""
        # حساب uptime
        uptime = "00:00:00"
        if hasattr(self, 'start_time') and self.start_time:
            current_time = time.time()
            uptime_seconds = int(current_time - self.start_time)
            hours = uptime_seconds // 3600
            minutes = (uptime_seconds % 3600) // 60
            seconds = uptime_seconds % 60
            uptime = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

        # تحديد الدقة
        resolution = f"{self.frame_width}x{self.frame_height}"

        # حساب bitrate تقريبي
        bitrate = 0
        if self.fps > 0:
            # تقدير تقريبي: دقة × fps × عامل ضغط
            pixels = self.frame_width * self.frame_height
            bitrate = int((pixels * self.fps * 0.1) / 1000)  # kbps

        return {
            'camera_id': self.camera_id,
            'is_active': self.is_active,
            'is_connected': self.is_connected,
            'fps': round(self.fps, 1),
            'resolution': resolution,
            'reconnect_attempts': self.reconnect_attempts,
            'last_frame_time': self.last_frame_time,
            'frame_queue_size': self.frame_queue.qsize(),
            'uptime': uptime,
            'bitrate': bitrate,
            'error_count': getattr(self, 'error_count', 0)
        }


class EnhancedCameraManager:
    """مدير الكاميرات المحسن"""
    
    def __init__(self):
        self.cameras: Dict[int, EnhancedCameraStream] = {}
        self.lock = threading.Lock()
        
        # إعدادات عامة
        self.max_cameras = 16
        self.default_quality = 70
        
        logger.info("تم إنشاء مدير الكاميرات المحسن")
    
    def add_camera(self, camera_id: int, rtsp_url: str, username: str = None, password: str = None) -> bool:
        """إضافة كاميرا جديدة"""
        try:
            with self.lock:
                if camera_id in self.cameras:
                    logger.warning(f"الكاميرا {camera_id} موجودة بالفعل")
                    return False
                
                if len(self.cameras) >= self.max_cameras:
                    logger.error(f"تم الوصول للحد الأقصى من الكاميرات ({self.max_cameras})")
                    return False
                
                camera = EnhancedCameraStream(camera_id, rtsp_url, username, password)
                self.cameras[camera_id] = camera
                
                logger.info(f"تم إضافة الكاميرا {camera_id}")
                return True
                
        except Exception as e:
            logger.error(f"فشل في إضافة الكاميرا {camera_id}: {e}")
            return False
    
    def start_camera(self, camera_id: int) -> bool:
        """بدء بث كاميرا"""
        with self.lock:
            if camera_id not in self.cameras:
                return False
            
            return self.cameras[camera_id].start()
    
    def stop_camera(self, camera_id: int) -> bool:
        """إيقاف بث كاميرا"""
        with self.lock:
            if camera_id not in self.cameras:
                return False
            
            self.cameras[camera_id].stop()
            return True
    
    def start_all_cameras(self) -> int:
        """بدء جميع الكاميرات"""
        started_count = 0
        with self.lock:
            for camera in self.cameras.values():
                if camera.start():
                    started_count += 1
        
        logger.info(f"تم بدء {started_count} كاميرا من أصل {len(self.cameras)}")
        return started_count
    
    def get_camera_frame(self, camera_id: int) -> Optional[bytes]:
        """الحصول على إطار من كاميرا محددة"""
        with self.lock:
            if camera_id not in self.cameras:
                return None
            
            return self.cameras[camera_id].get_latest_frame_jpeg()
    
    def get_all_cameras_status(self) -> Dict[int, dict]:
        """الحصول على حالة جميع الكاميرات"""
        with self.lock:
            return {
                camera_id: camera.get_status()
                for camera_id, camera in self.cameras.items()
            }
    
    def is_camera_active(self, camera_id: int) -> bool:
        """التحقق من نشاط كاميرا"""
        with self.lock:
            if camera_id not in self.cameras:
                return False
            
            return self.cameras[camera_id].is_active and self.cameras[camera_id].is_connected
