#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المراقبة الذكي - التطبيق الرئيسي
Smart Surveillance System - Main Application

المطور: نظام المراقبة الذكي
التاريخ: 2025
الإصدار: 1.0.0
"""

import os
import json
import logging
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import cv2
import threading
import time

# إعداد التطبيق
app = Flask(__name__)

# تحميل الإعدادات
def load_config():
    """تحميل ملف الإعدادات"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ ملف الإعدادات غير موجود!")
        return {}
    except json.JSONDecodeError:
        print("❌ خطأ في قراءة ملف الإعدادات!")
        return {}

config = load_config()

# إعداد Flask
app.config['SECRET_KEY'] = config.get('server', {}).get('secret_key', 'default-secret-key')
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{config.get('database', {}).get('path', 'database.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد SocketIO للبث المباشر
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
login_manager.login_message_category = 'info'

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    """نموذج المستخدم"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, user, viewer
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Camera(db.Model):
    """نموذج الكاميرا"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    url = db.Column(db.String(500), nullable=False)
    type = db.Column(db.String(20), nullable=False)  # rtsp, usb, http
    username = db.Column(db.String(50))
    password = db.Column(db.String(50))
    is_active = db.Column(db.Boolean, default=True)
    position_x = db.Column(db.Float, default=0)
    position_y = db.Column(db.Float, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Recording(db.Model):
    """نموذج التسجيل"""
    id = db.Column(db.Integer, primary_key=True)
    camera_id = db.Column(db.Integer, db.ForeignKey('camera.id'), nullable=False)
    filename = db.Column(db.String(200), nullable=False)
    start_time = db.Column(db.DateTime, nullable=False)
    end_time = db.Column(db.DateTime)
    file_size = db.Column(db.Integer)
    trigger_type = db.Column(db.String(20))  # manual, motion, schedule
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Event(db.Model):
    """نموذج الأحداث"""
    id = db.Column(db.Integer, primary_key=True)
    camera_id = db.Column(db.Integer, db.ForeignKey('camera.id'), nullable=False)
    event_type = db.Column(db.String(50), nullable=False)  # motion, face, object
    description = db.Column(db.Text)
    confidence = db.Column(db.Float)
    image_path = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# متغيرات عامة
camera_streams = {}
recording_threads = {}
motion_detectors = {}

# الصفحات الأساسية
@app.route('/')
@login_required
def index():
    """الصفحة الرئيسية"""
    cameras = Camera.query.filter_by(is_active=True).all()
    recent_events = Event.query.order_by(Event.created_at.desc()).limit(10).all()
    return render_template('index.html', cameras=cameras, events=recent_events)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()
            logger.info(f"تسجيل دخول ناجح للمستخدم: {username}")
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
            logger.warning(f"محاولة دخول فاشلة للمستخدم: {username}")
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logger.info(f"تسجيل خروج للمستخدم: {current_user.username}")
    logout_user()
    return redirect(url_for('login'))

@app.route('/cameras')
@login_required
def cameras():
    """صفحة إدارة الكاميرات"""
    cameras = Camera.query.all()
    return render_template('cameras.html', cameras=cameras)

@app.route('/recordings')
@login_required
def recordings():
    """صفحة التسجيلات"""
    recordings = Recording.query.order_by(Recording.created_at.desc()).all()
    return render_template('recordings.html', recordings=recordings)

@app.route('/events')
@login_required
def events():
    """صفحة الأحداث"""
    events = Event.query.order_by(Event.created_at.desc()).all()
    return render_template('events.html', events=events)

@app.route('/settings')
@login_required
def settings():
    """صفحة الإعدادات"""
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('index'))
    return render_template('settings.html', config=config)

# API endpoints
@app.route('/api/cameras', methods=['GET', 'POST'])
@login_required
def api_cameras():
    """API إدارة الكاميرات"""
    if request.method == 'POST':
        data = request.json
        camera = Camera(
            name=data['name'],
            url=data['url'],
            type=data['type'],
            username=data.get('username'),
            password=data.get('password')
        )
        db.session.add(camera)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم إضافة الكاميرا بنجاح'})
    
    cameras = Camera.query.all()
    return jsonify([{
        'id': c.id,
        'name': c.name,
        'url': c.url,
        'type': c.type,
        'is_active': c.is_active
    } for c in cameras])

# إنشاء قاعدة البيانات والمستخدم الافتراضي
def init_database():
    """إنشاء قاعدة البيانات والبيانات الافتراضية"""
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            logger.info("تم إنشاء المستخدم الافتراضي: admin/admin123")

if __name__ == '__main__':
    # إنشاء المجلدات المطلوبة
    os.makedirs('logs', exist_ok=True)
    os.makedirs('recordings', exist_ok=True)
    os.makedirs('known_faces', exist_ok=True)
    
    # إنشاء قاعدة البيانات
    init_database()
    
    # تشغيل التطبيق
    host = config.get('server', {}).get('host', '0.0.0.0')
    port = config.get('server', {}).get('port', 5000)
    debug = config.get('system', {}).get('debug', True)
    
    logger.info(f"🚀 بدء تشغيل نظام المراقبة الذكي على {host}:{port}")
    socketio.run(app, host=host, port=port, debug=debug, allow_unsafe_werkzeug=True)
